// <auto-generated> This file has been auto generated by xunit.v3.core. </auto-generated>

[assembly: global::Xunit.Runner.Common.RegisterRunnerReporter(typeof(global::Xunit.Runner.Common.AppVeyorReporter))]
[assembly: global::Xunit.Runner.Common.RegisterRunnerReporter(typeof(global::Xunit.Runner.Common.DefaultRunnerReporter))]
[assembly: global::Xunit.Runner.Common.RegisterRunnerReporter(typeof(global::Xunit.Runner.Common.JsonReporter))]
[assembly: global::Xunit.Runner.Common.RegisterRunnerReporter(typeof(global::Xunit.Runner.Common.QuietReporter))]
[assembly: global::Xunit.Runner.Common.RegisterRunnerReporter(typeof(global::Xunit.Runner.Common.SilentReporter))]
[assembly: global::Xunit.Runner.Common.RegisterRunnerReporter(typeof(global::Xunit.Runner.Common.TeamCityReporter))]
[assembly: global::Xunit.Runner.Common.RegisterRunnerReporter(typeof(global::Xunit.Runner.Common.VerboseReporter))]
[assembly: global::Xunit.Runner.Common.RegisterRunnerReporter(typeof(global::Xunit.Runner.Common.VstsReporter))]
