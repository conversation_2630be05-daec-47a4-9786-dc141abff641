<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AnalysisLevel>latest</AnalysisLevel>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Vsp.ApiBase" Version="9.1.6" />
    <PackageReference Include="Vsp.AuthorizationService.Internal.ApiProtocol" Version="9.0.1" />
    <PackageReference Include="Vsp.Entities.EntityFramework" Version="9.0.0" />
    <PackageReference Include="Vsp.Infrastructure" Version="9.0.0" />
  </ItemGroup>
</Project>
