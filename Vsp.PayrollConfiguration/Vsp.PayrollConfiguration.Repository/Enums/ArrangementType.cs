namespace Vsp.PayrollConfiguration.Repository.Enums;

/// <summary>
/// All values from <see cref="CtArrangementType"/>
/// </summary>
public enum ArrangementType
{
    /// <summary>
    /// Fonds
    /// </summary>
    Fund = 1,

    /// <summary>
    /// Fonds ABP
    /// </summary>
    AbpFund = 2,

    /// <summary>
    /// Grondslag
    /// </summary>
    BaseForCalculation = 3,

    // NOTE: This entity is OBSOLETE and will not be included from Classic!
    // <summary>
    // Vakantiefonds
    // </summary>
    // HolidayFund = 4,

    /// <summary>
    /// Overwerk
    /// </summary>
    Overtime = 5,

    /// <summary>
    /// Toeslag
    /// </summary>
    Benefit = 6,
}