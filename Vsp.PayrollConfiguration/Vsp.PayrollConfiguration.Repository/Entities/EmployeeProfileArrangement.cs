namespace Vsp.PayrollConfiguration.Repository.Entities;

public class EmployeeProfileArrangement : GeneratedIdEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int EmployeeProfileId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int ArrangementType { get; set; }
    [GeneratedIdKey<short>(4)]
    public int Identification { get; set; }

    public EmployeeProfile EmployeeProfile { get; set; } = null!;
}