namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ModelComponentDeviantDescriptionConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelComponentDeviantDescription>
{
    public override void Configure(EntityTypeBuilder<ModelComponentDeviantDescription> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelComponentNaamAfwijkend", "Ulsa", x => x.<PERSON>("trigger"));
        builder.Has<PERSON>ey(x => new { x.InheritanceLevelId, x.ComponentId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
        builder.Property(x => x.Description).HasColumnName("Naam");
    }
}