namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ComponentGeneralLedgerAccountConfiguration : IdEntityTypeConfigurationBase<ComponentGeneralLedgerAccount>
{
    public override void Configure(EntityTypeBuilder<ComponentGeneralLedgerAccount> builder)
    {
        base.Configure(builder);

        builder.ToTable("ComponentGrootboekrekening", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.InheritanceLevelId, x.YearId, x.ComponentId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");

        builder.HasOne(x => x.ModelComponent).WithMany().IsRequired(false).HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ComponentId });
    }
}