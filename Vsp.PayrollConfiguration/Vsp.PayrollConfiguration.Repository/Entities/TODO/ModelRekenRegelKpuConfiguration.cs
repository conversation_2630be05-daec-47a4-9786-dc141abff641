namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelRekenRegelKpuConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelRekenRegelKpu>
{
    public override void Configure(EntityTypeBuilder<ModelRekenRegelKpu> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelRekenRegelKpu", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.J<PERSON>, x.<PERSON> });
    }
}
