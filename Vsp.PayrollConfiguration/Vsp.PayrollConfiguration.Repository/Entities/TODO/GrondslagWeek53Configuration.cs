namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class GrondslagWeek53Configuration : GeneratedIdEntityTypeConfigurationBase<GrondslagWeek53>
{
    public override void Configure(EntityTypeBuilder<GrondslagWeek53> builder)
    {
        base.Configure(builder);

        builder.ToTable("GrondslagWeek53", "Ulsa");
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.<PERSON>, x.GrondslagID });
    }
}
