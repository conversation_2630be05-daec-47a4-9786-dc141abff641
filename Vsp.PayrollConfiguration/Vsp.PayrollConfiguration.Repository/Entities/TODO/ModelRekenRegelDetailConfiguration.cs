namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelRekenRegelDetailConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelRekenRegelDetail>
{
    public override void Configure(EntityTypeBuilder<ModelRekenRegelDetail> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelRekenRegelDetail", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON>ey(x => new { x.WerkgeverID, x.<PERSON>, x.<PERSON>RegelID, x.RekenRegelDetailID });
    }
}
