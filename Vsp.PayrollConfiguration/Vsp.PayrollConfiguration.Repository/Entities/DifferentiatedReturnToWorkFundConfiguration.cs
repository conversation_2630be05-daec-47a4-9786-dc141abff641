namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class DifferentiatedReturnToWorkFundConfiguration : IdEntityTypeConfigurationBase<DifferentiatedReturnToWorkFund>
{
    public override void Configure(EntityTypeBuilder<DifferentiatedReturnToWorkFund> builder)
    {
        base.Configure(builder);

        builder.ToTable("WgaGedifferentieerd", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");
        builder.Property(x => x.WgaTotalContribution).HasColumnName("AfdrachtPercentageWgaVast").HasPrecision(6, 3);
        builder.Property(x => x.WgaEmploymentContribution).HasColumnName("PremiePercentageWgaVast").HasPrecision(6, 3);
        builder.Property(x => x.ZwTotalContribution).HasColumnName("AfdrachtPercentageZwFlex").HasPrecision(6, 3);

        builder.HasOne(x => x.Administration).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
        builder.HasOne(x => x.VwPayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
    }
}

