namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class AbpFundConfiguration : GeneratedIdEntityTypeConfigurationBase<AbpFund>
{
    public override void Configure(EntityTypeBuilder<AbpFund> builder)
    {
        base.Configure(builder);

        builder.ToTable("FondsABP", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, x.AbpFundId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.AbpFundId).HasColumnName("FondsABPID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");

        builder.Property(x => x.TotalContribution).HasColumnName("AfdrachtPercentage");
        builder.Property(x => x.EmploymentContribution).HasColumnName("PremiePercentage");
        builder.Property(x => x.Franchise).HasColumnName("Franchise");
        builder.Property(x => x.FranchiseUpToAge40).HasColumnName("FranchiseTot40Jaar");
        builder.Property(x => x.FranchiseUpToAge50).HasColumnName("FranchiseTot50Jaar");

        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
        builder.HasOne(x => x.CtAbpFund).WithMany().HasForeignKey(x => x.AbpFundId);
    }
}