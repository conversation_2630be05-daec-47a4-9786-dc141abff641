namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class UnitPercentageConfiguration : GeneratedIdEntityTypeConfigurationBase<UnitPercentage>
{
    public override void Configure(EntityTypeBuilder<UnitPercentage> builder)
    {
        base.Configure(builder);

        builder.ToTable("EenheidPercentage", "Ulsa");
        builder.<PERSON><PERSON>ey(x => new { x.InheritanceLevelId, x.YearId, x.ComponentId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");

        builder.Property(x => x.ComponentIdDefinedAtLevel).HasColumnName("ComponentIDAttribuut");
        builder.Property(x => x.Percentage).HasPrecision(6, 3);
        builder.Property(x => x.PercentageDefinedAtLevel).HasColumnName("PercentageAttribuut");
        builder.Property(x => x.CalculateOver).HasColumnName("BerekenenOver");
        builder.Property(x => x.CalculateOverDefinedAtLevel).HasColumnName("BerekenenOverAttribuut");

        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.Component).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ComponentId });
        builder.HasOne(x => x.ModelComponent).WithMany().IsRequired(false).HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ComponentId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
        builder.HasOne(x => x.CtCalculateOver).WithMany().HasForeignKey(x => x.CalculateOver);
    }
}