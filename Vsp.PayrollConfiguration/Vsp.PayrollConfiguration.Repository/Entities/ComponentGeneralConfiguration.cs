namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ComponentGeneralConfiguration : GeneratedIdEntityTypeConfigurationBase<ComponentGeneral>
{
    public override void Configure(EntityTypeBuilder<ComponentGeneral> builder)
    {
        base.Configure(builder);

        builder.ToTable("ComponentAlgemeen", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(e => e.ComponentId);

        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
        builder.Property(x => x.Description).HasColumnName("Naam");
        builder.Property(x => x.DeductionOrPayment).HasColumnName("BetalingInhouding");
        builder.Property(x => x.PaymentPeriod).HasColumnName("BetalingsPeriode");
        builder.Property(x => x.TaxLiable).HasColumnName("BelastingPlichtig");
        builder.Property(x => x.SocialSecurityLiable).HasColumnName("SociaalPlichtig");
        builder.Property(x => x.HoursIndication).HasColumnName("UrenIndicatie");
        builder.Property(x => x.CostsEmployer).HasColumnName("KostenWerkgever");
        builder.Property(x => x.IsFullTime).HasColumnName("Fulltime");
        builder.Property(x => x.IsBaseForCalculationOvertime).HasColumnName("GrondslagOverwerk");
        builder.Property(x => x.IsTravelExpense).HasColumnName("OnkostenVergoeding");
        builder.Property(x => x.SuppressPrinting).HasColumnName("PrintenOnderdrukken");
        builder.Property(x => x.SuppressPrintingAccumulations).HasColumnName("PrintenCumulatieOnderdrukken");
        builder.Property(x => x.BaseForCalculationBter).HasColumnName("GrondslagBterLoon");
        builder.Property(x => x.IsPayment).HasColumnName("Excasso");
        builder.Property(x => x.IsOvertime).HasColumnName("OverwerkLoonaangifte");
        builder.Property(x => x.BalanceSheetSide).HasColumnName("Journaal");
        builder.Property(x => x.Category).HasColumnName("Categorie");

        builder.Property(x => x.IsLifeSpanScheme).HasColumnName("LevensloopLoonaangifte");
        builder.Property(x => x.Order).HasColumnName("Volgorde");
        builder.Property(x => x.StandaardZichtbaar).HasColumnName("StandaardZichtbaar");

        builder.HasOne(x => x.CtCategory).WithMany().HasForeignKey(x => x.Category);
    }
}