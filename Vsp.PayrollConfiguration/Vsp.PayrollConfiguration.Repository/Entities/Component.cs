namespace Vsp.PayrollConfiguration.Repository.Entities;

public class Component : GeneratedIdEntity, IInheritanceEntity, IYearEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int ComponentId { get; set; }

    public string Description { get; set; } = null!;
    public int DeductionOrPayment { get; set; }
    public int PaymentPeriod { get; set; }
    public int TaxLiable { get; set; }
    public int SocialSecurityLiable { get; set; }
    public int HoursIndication { get; set; }
    public int CostsEmployer { get; set; }
    public int IsNetToGross { get; set; }
    public int IsFullTime { get; set; }
    public int IsBaseForCalculationOvertime { get; set; }
    public int IsTravelExpense { get; set; }
    public int SuppressPrinting { get; set; }
    public int SuppressPrintingAccumulations { get; set; }
    public int IsBaseForCalculationDailyWageZw { get; set; }
    public int IsBaseForCalculationDailyWageSupplement { get; set; }
    public int BaseForCalculationBter { get; set; }
    public int IsPayment { get; set; }
    public string? PaymentDescription { get; set; }
    public int IsOvertime { get; set; }
    public int Column { get; set; }
    public int BalanceSheetSide { get; set; }
    public int Category { get; set; }

    public int ComponentIdDefinedAtLevel { get; set; }
    public int DescriptionDefinedAtLevel { get; set; }
    public int DeductionOrPaymentDefinedAtLevel { get; set; }
    public int PaymentPeriodDefinedAtLevel { get; set; }
    public int TaxLiableDefinedAtLevel { get; set; }
    public int SocialSecurityLiableDefinedAtLevel { get; set; }
    public int HoursIndicationDefinedAtLevel { get; set; }
    public int CostsEmployerDefinedAtLevel { get; set; }
    public int IsNetToGrossDefinedAtLevel { get; set; }
    public int IsFullTimeDefinedAtLevel { get; set; }
    public int IsBaseForCalculationOvertimeDefinedAtLevel { get; set; }
    public int IsTravelExpenseDefinedAtLevel { get; set; }
    public int SuppressPrintingDefinedAtLevel { get; set; }
    public int SuppressPrintingAccumulationsDefinedAtLevel { get; set; }
    public int IsBaseForCalculationDailyWageZwDefinedAtLevel { get; set; }
    public int IsBaseForCalculationDailyWageSupplementDefinedAtLevel { get; set; }
    public int BaseForCalculationBterDefinedAtLevel { get; set; }
    public int IsPaymentDefinedAtLevel { get; set; }
    public int PaymentDescriptionDefinedAtLevel { get; set; }
    public int IsOvertimeDefinedAtLevel { get; set; }
    public int ColumnDefinedAtLevel { get; set; }
    public int BalanceSheetSideDefinedAtLevel { get; set; }
    public int CategoryDefinedAtLevel { get; set; }

    // Properties not in API but in DB
    public int? GeneralLedgerAccountNumber { get; set; }
    public int IsLifeSpanScheme { get; set; }
    public int Order { get; set; }
    public int IsVisibleByDefault { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public Year Year { get; set; } = null!;

    public CtDeductionOrPayment CtDeductionOrPayment { get; set; } = null!;
    public CtPaymentPeriod CtPaymentPeriod { get; set; } = null!;
    public CtTaxLiable CtTaxLiable { get; set; } = null!;
    public CtSocialSecurityLiable CtSocialSecurityLiable { get; set; } = null!;
    public CtHoursIndication CtHoursIndication { get; set; } = null!;
    public CtCostsEmployer CtCostsEmployer { get; set; } = null!;
    public CtYesNo CtIsNetToGross { get; set; } = null!;
    public CtYesNo CtIsFullTime { get; set; } = null!;
    public CtYesNo CtIsBaseForCalculationOvertime { get; set; } = null!;
    public CtYesNo CtIsTravelExpense { get; set; } = null!;
    public CtYesNo CtSuppressPrinting { get; set; } = null!;
    public CtYesNo CtSuppressPrintingAccumulations { get; set; } = null!;
    public CtYesNo CtIsBaseForCalculationDailyWageZw { get; set; } = null!;
    public CtYesNo CtIsBaseForCalculationDailyWageSupplement { get; set; } = null!;
    public CtBaseForCalculationBter CtBaseForCalculationBter { get; set; } = null!;
    public CtYesNo CtIsPayment { get; set; } = null!;
    public CtYesNo CtIsOvertime { get; set; } = null!;
    public CtColumn CtColumn { get; set; } = null!;
    public CtBalanceSheetSide CtBalanceSheetSide { get; set; } = null!;
    public CtCategory CtCategory { get; set; } = null!;

    public VwComponentDeviant? VwComponentDeviant { get; set; }
    public VwComponentDeviantDescription? VwComponentDeviantDescription { get; set; }
    public ModelComponentDeviant? ModelComponentDeviant { get; set; }
    public ModelComponentDeviantDescription? ModelComponentDeviantDescription { get; set; }
}