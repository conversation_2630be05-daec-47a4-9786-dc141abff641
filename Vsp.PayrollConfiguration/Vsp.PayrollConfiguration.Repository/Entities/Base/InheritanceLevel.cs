namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

public class InheritanceLevel : IIdEntity
{
    public Guid Id { get; set; }

    public int InheritanceLevelId { get; set; }

    public int ParentInheritanceLevelId { get; set; }
    public int ProviderId { get; set; }

    public string? Description { get; set; }
    public string? Comment { get; set; }

    public InheritanceLevelInfo InheritanceLevelInfo { get; set; } = null!;
    public InheritanceLevel? ParentInheritanceLevel { get; set; }

    /// <summary>
    /// This relation only exists for payroll administrations, not for wage models or collective labor agreements!
    /// </summary>
    public Administration? Administration { get; set; }
    /// <summary>
    /// This relation only exists for payroll administrations and wage models, not for collective labor agreements!
    /// </summary>
    public Provider? Provider { get; set; }
}
