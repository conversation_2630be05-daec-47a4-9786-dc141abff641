namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

public class Administration : IIdEntity
{
    public Guid Id { get; set; }

    public int InheritanceLevelId { get; set; }

    public int EmployerId { get; set; }

    public string Name { get; set; } = null!;

    public int ClientNumber { get; set; }

    public string? AdministrationNumber { get; set; }

    public int GroupCode { get; set; }

    public string? WageTaxNumber { get; set; }


    public int PayrollId { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;

    public Employer Employer { get; set; } = null!;
}