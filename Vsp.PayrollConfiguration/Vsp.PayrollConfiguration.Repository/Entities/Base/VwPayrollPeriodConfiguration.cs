namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

internal class VwPayrollPeriodConfiguration : GeneratedIdEntityTypeConfigurationBase<VwPayrollPeriod>
{
    public override void Configure(EntityTypeBuilder<VwPayrollPeriod> builder)
    {
        base.Configure(builder);

        builder.ToView("vwVerloningsPeriode", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");

        builder.Property(x => x.Omschrijving).HasMaxLength(62).IsRequired(false);
    }
}