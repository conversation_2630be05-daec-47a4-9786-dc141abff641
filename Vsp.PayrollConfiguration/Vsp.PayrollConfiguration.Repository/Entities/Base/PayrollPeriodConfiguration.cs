namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

internal class PayrollPeriodConfiguration : GeneratedIdEntityTypeConfigurationBase<PayrollPeriod>
{
    public override void Configure(EntityTypeBuilder<PayrollPeriod> builder)
    {
        base.Configure(builder);

        builder.ToTable("VerloningsPeriode", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");
        builder.Property(x => x.StartDate).HasColumnName("DatumIngang");
        builder.Property(x => x.EndDate).HasColumnName("DatumEinde");
        builder.Property(x => x.YearPeriod).HasColumnName("JaarPeriode").HasComputedColumnSql("[JaarID]*(100)+[VerloningsPeriodeID]").ValueGeneratedOnAddOrUpdate();

        builder.HasOne(pp => pp.Year).WithMany(y => y.PayrollPeriods).HasForeignKey(pp => new { pp.InheritanceLevelId, pp.YearId });
    }
}