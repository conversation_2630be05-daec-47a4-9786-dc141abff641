namespace Vsp.PayrollConfiguration.Repository.Entities;

public class DifferentiatedReturnToWorkFund : IPayrollPeriodEntity
{
    public Guid Id { get; set; }
    public int InheritanceLevelId { get; set; }
    public int YearId { get; set; }
    public int PayrollPeriodId { get; set; }
    public decimal WgaTotalContribution { get; set; }
    public decimal WgaEmploymentContribution { get; set; }
    public decimal ZwTotalContribution { get; set; }

    public Administration Administration { get; set; } = null!;
    public Year Year { get; set; } = null!;
    public PayrollPeriod PayrollPeriod { get; set; } = null!;
    public VwPayrollPeriod VwPayrollPeriod { get; set; } = null!;
}