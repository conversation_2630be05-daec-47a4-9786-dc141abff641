namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class FundBasePayrollComponentConfiguration : GeneratedIdEntityTypeConfigurationBase<FundBasePayrollComponent>
{
    public override void Configure(EntityTypeBuilder<FundBasePayrollComponent> builder)
    {
        base.Configure(builder);

        builder.ToTable("FondsGrondslag", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, x.FundId, x.ComponentId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.FundId).HasColumnName("FondsID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");

        builder.HasOne(x => x.ModelComponent).WithMany().IsRequired(false).HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ComponentId });
    }
}