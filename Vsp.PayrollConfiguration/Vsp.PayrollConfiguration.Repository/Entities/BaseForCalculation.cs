namespace Vsp.PayrollConfiguration.Repository.Entities;

public class BaseForCalculation : GeneratedIdEntity, IInheritanceEntity, IPayrollPeriodEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int BaseForCalculationId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int PayrollPeriodId { get; set; }

    public string Description { get; set; } = null!;
    public int BaseType { get; set; }
    public int StartEmployeeAgeType { get; set; }
    public decimal StartEmployeeAge { get; set; }
    public int EndEmployeeAgeType { get; set; }
    public decimal EndEmployeeAge { get; set; }
    public int ResultPayrollComponentId { get; set; }
    public decimal Percentage { get; set; }
    public int CalculationPayrollPeriodNumber { get; set; }
    public int ReferencePayrollPeriodNumber { get; set; }
    public int PayoutPayrollPeriodNumber { get; set; }
    public int AccrualEndPayrollPeriodNumber { get; set; }
    public int PayslipType { get; set; }
    public int IsPayoutAtStartOfEmployment { get; set; }
    public int IsPayoutAtEndOfEmployment { get; set; }
    public int AdvancePayrollComponentId { get; set; }
    public decimal AdvancePercentage { get; set; }
    public int AdvancePayrollPeriodNumber { get; set; }
    public int PeriodicReservationPayrollComponentId { get; set; }
    public decimal FinancialReservationPercentage { get; set; }
    public decimal FinancialMarkupPercentage { get; set; }
    public int IsCumulativeCalculation { get; set; }
    public int IsPartTimeCalculation { get; set; }
    public int IsAutomaticCalculation { get; set; }
    public int IsSupplementingDailyWage { get; set; }
    public int MinimumMaximumType { get; set; }

    // DefinedAtLevel properties
    public int BaseForCalculationIdDefinedAtLevel { get; set; }
    public int DescriptionDefinedAtLevel { get; set; }
    public int BaseTypeDefinedAtLevel { get; set; }
    public int StartEmployeeAgeTypeDefinedAtLevel { get; set; }
    public int StartEmployeeAgeDefinedAtLevel { get; set; }
    public int EndEmployeeAgeTypeDefinedAtLevel { get; set; }
    public int EndEmployeeAgeDefinedAtLevel { get; set; }
    public int ResultPayrollComponentDefinedAtLevel { get; set; }
    public int PercentageDefinedAtLevel { get; set; }
    public int CalculationPayrollPeriodDefinedAtLevel { get; set; }
    public int ReferencePayrollPeriodDefinedAtLevel { get; set; }
    public int PayoutPayrollPeriodDefinedAtLevel { get; set; }
    public int AccrualEndPayrollPeriodDefinedAtLevel { get; set; }
    public int PayslipTypeDefinedAtLevel { get; set; }
    public int IsPayoutAtStartOfEmploymentDefinedAtLevel { get; set; }
    public int IsPayoutAtEndOfEmploymentDefinedAtLevel { get; set; }
    public int AdvancePayrollComponentDefinedAtLevel { get; set; }
    public int AdvancePercentageDefinedAtLevel { get; set; }
    public int AdvancePayrollPeriodNumberDefinedAtLevel { get; set; }
    public int PeriodicReservationPayrollComponentDefinedAtLevel { get; set; }
    public int FinancialReservationPercentageDefinedAtLevel { get; set; }
    public int FinancialMarkupPercentageDefinedAtLevel { get; set; }
    public int IsCumulativeCalculationDefinedAtLevel { get; set; }
    public int IsPartTimeCalculationDefinedAtLevel { get; set; }
    public int IsAutomaticCalculationDefinedAtLevel { get; set; }
    public int IsSupplementingDailyWageDefinedAtLevel { get; set; }
    public int MinimumMaximumTypeDefinedAtLevel { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public Year Year { get; set; } = null!;
    public PayrollPeriod PayrollPeriod { get; set; } = null!;
    public Component ResultPayrollComponent { get; set; } = null!;
    public Component? AdvancePayrollComponent { get; set; }
    public Component? PeriodicReservationPayrollComponent { get; set; }

    public CtBaseType? CtBaseType { get; set; }
    public CtEmployeeAgeType? CtStartEmployeeAgeType { get; set; }
    public CtEmployeeAgeType? CtEndEmployeeAgeType { get; set; }
    public CtPayslipType CtPayslipType { get; set; } = null!;
    public CtMinimumMaximumType CtMinimumMaximumType { get; set; } = null!;
}