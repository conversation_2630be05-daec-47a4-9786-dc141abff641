namespace Vsp.PayrollConfiguration.Repository.Entities.Loket;

internal class EmploymentConfiguration : IdEntityTypeConfigurationBase<Employment>
{
    public override void Configure(EntityTypeBuilder<Employment> builder)
    {
        base.Configure(builder);

        builder.ToTable("Dienstverband", "Loket");
        builder.<PERSON><PERSON>ey(x => x.EmployerId);
        builder.<PERSON><PERSON><PERSON>(x => x.EmploymentId);
        builder.<PERSON><PERSON><PERSON>(x => x.PersonId);

        builder.Property(x => x.EmployerId).HasColumnName("WerkgeverID");
        builder.Property(x => x.EmploymentId).HasColumnName("DienstverbandID");
        builder.Property(x => x.PersonId).HasColumnName("PersoonID");
        builder.Property(x => x.PayrollId).HasColumnName("VerloningID");
    }
}
