using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Models;
using Vsp.PayrollConfiguration.Infrastructure.Queries;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Queries;

internal class GetBaseForCalculationAgeBasedMaximumQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : GetInheritanceEntityQuery<BaseForCalculationAgeBasedMaximumModel, Repository.Entities.BaseForCalculationAgeBasedMaximum>(dependencies);
