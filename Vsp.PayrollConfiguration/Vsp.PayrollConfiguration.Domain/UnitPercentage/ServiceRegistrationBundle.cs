using Vsp.PayrollConfiguration.Domain.UnitPercentage.Authorizations;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Commands;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Helpers;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Interfaces;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Queries;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Services;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Validators;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<UnitPercentageAuthorizationModel>, UnitPercentageAuthorization>();

        services.AddScoped<IUnitPercentageService, UnitPercentageService>();

        services.AddScoped<IUnitPercentageMetadataHelper, UnitPercentageMetadataHelper>();

        services.AddScoped<IValidator<UnitPercentagePostModel>, InsertUnitPercentageValidator>();
        services.AddScoped<IValidator<UnitPercentagePatchModel>, PatchUnitPercentageValidator>();
        services.AddScoped<IValidator<ModelUnitPercentage>, DeleteUnitPercentageValidator>();

        services.AddScoped<IFilteredQuery<UnitPercentageModel, Repository.Entities.UnitPercentage, Repository.Entities.Year, ILoketContext>, GetUnitPercentageByYearIdQuery>();
        services.AddScoped<IGetInheritanceEntityQuery<UnitPercentageModel, Repository.Entities.UnitPercentage>, GetUnitPercentageQuery>();
        services.AddScoped<IInsertInheritanceEntityCommand<UnitPercentagePostModel, UnitPercentageModel, Repository.Entities.UnitPercentage, ModelUnitPercentage>, InsertUnitPercentageCommand>();
        services.AddScoped<IPatchInheritanceEntityCommand<UnitPercentagePatchModel, UnitPercentageModel, Repository.Entities.UnitPercentage, ModelUnitPercentage>, PatchUnitPercentageCommand>();
        services.AddScoped<IDeleteInheritanceEntityCommand<ModelUnitPercentage>, DeleteUnitPercentageCommand>();
    }
}