using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Models;

public class AbpFundPostModel : AbpFundPatchModel, IYearPostModel
{
    [JsonIgnore]
    public Guid InheritanceLevelId { get; set; }
    
    [Required]
    public int? Key { get; set; } = null!;

    [Required] 
    [Range(1900, 9999)]
    public int? Year { get; set; } = null!;
    
    [Required] 
    public PayrollPeriod StartPayrollPeriod { get; set; } = null!;
}
