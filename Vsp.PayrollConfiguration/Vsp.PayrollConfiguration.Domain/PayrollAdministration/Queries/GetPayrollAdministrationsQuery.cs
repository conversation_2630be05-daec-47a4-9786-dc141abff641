using Vsp.Commands.Enums;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.PayrollAdministration.Queries;

internal class GetPayrollAdministrationsQuery(IFilteredQueryWithGlobalFilterDependencies<ILoketContext> dependencies)
    : FilteredQueryWithGlobalFilter<PayrollAdministrationModel, Repository.Entities.Base.InheritanceLevel, Repository.Entities.Base.InheritanceLevel, ILoketContext>(dependencies)
{
    public override Expression<Func<Repository.Entities.Base.InheritanceLevel, bool>>? FilterCollectionByExpression(Guid id) =>
        // This check filters "corrupt" entries in the database: payroll administrations that exist in Ulsa.ModelWerkgever but not in Ulsa.Werkgever
        il => il.Administration != null
            && il.InheritanceLevelInfo.Type == (int)InheritanceLevel.PayrollAdministration
            // ClientNummer 0 is a Non-Payroll administration, so we filter this one out
            && il.Administration.ClientNumber != 0
            // The user should be part of a team within the employer that is coupled to this administration
            && il.Administration.Employer.TeamEmployers.Any(te => te.Team.TeamUsers.Any(tu => tu.User.Id == id));

    protected override IQueryable<Repository.Entities.Base.InheritanceLevel> ApplyRoleFilter(IQueryable<Repository.Entities.Base.InheritanceLevel> query, ICurrentContext user)
    {
        query = user.Rol switch
        {
            RolEnum.Provider => query,
            _ => throw new ArgumentException($"Role {user.Rol} is not allowed."),
        };
        return base.ApplyRoleFilter(query, user);
    }

    // The expression func returned from this method is never called but instead reflection is used to determine the field that should be used to query, thus we can just ignore nullable fields.
    protected override Expression<Func<Repository.Entities.Base.InheritanceLevel, Guid>> GetPathToFilterIdForGlobalFilter() => il => il.Administration!.Id;
    protected override GlobalFilterLevel FilterLevel { get; } = GlobalFilterLevel.PayrollAdministration;
}
