using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Authorizations;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Interfaces;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Queries;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Services;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.PayrollAdministration;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<PayrollAdministrationAuthorizationModel>, PayrollAdministrationAuthorization>();
        services.AddScoped<IPayrollAdministrationService, PayrollAdministrationService>();
        services.AddScoped<IFilteredQuery<PayrollAdministrationModel, InheritanceLevel, InheritanceLevel, ILoketContext>, GetPayrollAdministrationsQuery>();
        services.AddScoped<IGetByIdQuery<PayrollAdministrationModel, InheritanceLevel, ILoketContext>, GetPayrollAdministrationByIdQuery>();
    }
}
