using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Queries;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Queries;

internal class GetBaseForCalculationBasePayrollComponentQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : GetInheritanceEntityQuery<BaseForCalculationBasePayrollComponentModel,
        Repository.Entities.BaseForCalculationBasePayrollComponent>(dependencies)
{
    public override Expression<Func<Repository.Entities.BaseForCalculationBasePayrollComponent, bool>>? FilterCollectionByExpression(Guid baseForCalculationId)
    {
        var baseForCalculation = new Repository.Entities.BaseForCalculation { Id = baseForCalculationId };
        GeneratedIdHelper.GenerateIdKeys(baseForCalculation);

        return x =>
            x.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
            x.YearId == baseForCalculation.YearId &&
            x.BaseForCalculationId == baseForCalculation.BaseForCalculationId;
    }
}