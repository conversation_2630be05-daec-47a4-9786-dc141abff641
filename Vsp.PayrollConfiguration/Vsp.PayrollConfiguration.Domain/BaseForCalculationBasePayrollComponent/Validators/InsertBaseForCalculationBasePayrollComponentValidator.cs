
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Validators;

internal class
    InsertBaseForCalculationBasePayrollComponentValidator : AbstractValidator<
    BaseForCalculationBasePayrollComponentPostModel>
{
    private readonly ILoketContext loketContext;

    public InsertBaseForCalculationBasePayrollComponentValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;

        // Include the standard inheritance entity validator
        Include(new InsertInheritanceEntityValidator<BaseForCalculationBasePayrollComponentPostModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent>(lo<PERSON><PERSON><PERSON><PERSON><PERSON>, mapper));
       
    }
}