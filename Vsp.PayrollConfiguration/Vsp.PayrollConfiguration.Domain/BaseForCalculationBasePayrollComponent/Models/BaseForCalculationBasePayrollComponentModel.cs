using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;

public class BaseForCalculationBasePayrollComponentModel
{
    public Guid Id { get; set; }

    public InheritanceLevelModel InheritanceLevel { get; set; } = null!;

    public KeyModel BaseForCalculation { get; set; } = null!;

    public PayrollComponentMinimizedModel PayrollComponent { get; set; } = null!;

    public int Year { get; set; }

    public PayrollPeriodModel StartPayrollPeriod { get; set; } = null!;

    public KeyValueModel Origin { get; set; } = null!;

    public BaseForCalculationBasePayrollComponentDefinedAtLevelModel DefinedAtLevel { get; set; } = null!;
}

public class BaseForCalculationBasePayrollComponentDefinedAtLevelModel
{
    public InheritanceLevelTypeModel Id { get; set; } = null!;
    public InheritanceLevelTypeModel Origin { get; set; } = null!;
}