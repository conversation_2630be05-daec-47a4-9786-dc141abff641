using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Commands;

internal class DeleteBaseForCalculationBasePayrollComponentCommand(IDeleteCommandDependencies<ILoketContext> dependencies)
    : DeleteInheritanceEntityCommand<ModelBaseForCalculationBasePayrollComponent>(dependencies);