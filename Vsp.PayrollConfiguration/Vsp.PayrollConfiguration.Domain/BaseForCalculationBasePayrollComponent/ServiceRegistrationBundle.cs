using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Authorizations;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Commands;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Helpers;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Queries;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Services;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Validators;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent;

public  class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<BaseForCalculationBasePayrollComponentAuthorizationModel>, BaseForCalculationBasePayrollComponentAuthorization>();
        services.AddScoped<IBaseForCalculationBasePayrollComponentHelper, BaseForCalculationBasePayrollComponentHelper>();
        services.AddScoped<IBaseForCalculationBasePayrollComponentService, BaseForCalculationBasePayrollComponentService>();

        services.AddScoped<IGetInheritanceEntityQuery<BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent>, GetBaseForCalculationBasePayrollComponentQuery>();
        services.AddScoped<IDeleteInheritanceEntityCommand<ModelBaseForCalculationBasePayrollComponent>, DeleteBaseForCalculationBasePayrollComponentCommand>();
        services.AddScoped<IInsertInheritanceEntityCommand<BaseForCalculationBasePayrollComponentPostModel, BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent>, InsertBaseForCalculationBasePayrollComponentCommand>();
        services.AddScoped<IPatchInheritanceEntityCommand<BaseForCalculationBasePayrollComponentPatchModel, BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent>, PatchBaseForCalculationBasePayrollComponentCommand>();
        services.AddScoped<IGetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery, GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery>();
        
        services.AddScoped<IValidator<BaseForCalculationBasePayrollComponentPostModel>, InsertBaseForCalculationBasePayrollComponentValidator>();
        services.AddScoped<IValidator<BaseForCalculationBasePayrollComponentPatchModel>, PatchBaseForCalculationBasePayrollComponentValidator>();
    }
}