using Vsp.PayrollConfiguration.Domain.WageModel.Interfaces;
using Vsp.PayrollConfiguration.Domain.WageModel.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.WageModel.Services;

internal class WageModelService(
    ICurrentContext currentContext,
    IFilteredQuery<WageModelModel, InheritanceLevel, InheritanceLevel, ILoketContext> getWageModelsQuery,
    IGetByIdQuery<WageModelModel, InheritanceLevel, ILoketContext> getByIdQuery
) : IWageModelService
{
    private readonly ICurrentContext currentContext = currentContext;
    private readonly IFilteredQuery<WageModelModel, InheritanceLevel, InheritanceLevel, ILoketContext> getWageModelsQuery = getWageModelsQuery;
    private readonly IGetByIdQuery<WageModelModel, InheritanceLevel, ILoketContext> getByIdQuery = getByIdQuery;

    public async Task<IListOperationResult<WageModelModel>> GetWageModelsByBearerTokenAsync() =>
        await this.getWageModelsQuery.ExecuteList(this.currentContext.UserId);

    public async Task<IOperationResult<WageModelModel>> GetWageModelByWageModelIdAsync(Guid wageModelId) =>
        await this.getByIdQuery.Execute(wageModelId);
}
