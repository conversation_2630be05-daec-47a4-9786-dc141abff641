using Vsp.PayrollConfiguration.Domain.WageModel.Models;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.WageModel.Mappers;

internal class WageModelProfile : Profile
{
    public WageModelProfile()
    {
        CreateMap<InheritanceLevel, WageModelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description ?? ""))
            .ForMember(dst => dst.Comment, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.Comment) ? src.Comment : null))
            .ForMember(dst => dst.CollectiveLaborAgreement, opt => opt.MapFrom(src => src.ParentInheritanceLevel!));
    }
}
