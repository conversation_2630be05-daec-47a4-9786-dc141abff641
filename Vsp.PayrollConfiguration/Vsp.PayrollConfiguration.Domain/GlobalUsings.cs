global using System.ComponentModel.DataAnnotations;
global using System.Linq.Expressions;
global using System.Reflection;
global using AutoMapper;
global using FluentValidation;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.DependencyInjection;
global using Newtonsoft.Json;
global using Vsp.ApiBase.Interfaces;
global using Vsp.AuthorizationService.Internal.ApiProtocol.Authorization;
global using Vsp.Commands;
global using Vsp.Entities.GeneratedId;
global using Vsp.Infrastructure;
global using Vsp.PayrollConfiguration.Repository.Entities;
global using Vsp.PayrollConfiguration.Repository.Entities.Base;
global using Vsp.PayrollConfiguration.Repository.Entities.Loket;
global using Vsp.Validation;
