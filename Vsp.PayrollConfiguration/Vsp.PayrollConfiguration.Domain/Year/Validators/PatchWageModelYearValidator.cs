using Vsp.PayrollConfiguration.Domain.Year.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Year.Validators;

/// <summary>
/// Validator for the YearPatchModel.
/// </summary>
internal class PatchWageModelYearValidator : AbstractValidator<YearWmPatchModel>
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;

    public PatchWageModelYearValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;
        this.mapper = mapper;

        // Rule to check if the StandardShift property is valid.
        RuleFor(x => x)
            .MustAsync(BeValidShiftNumberAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_StandardShift_Invalid)
            .WithMessage("standardShift.shiftNumber is invalid.");

        // Rule to check if the StandardEmployeeProfile property is valid.
        RuleFor(x => x)
            .MustAsync(BeValidEmployeeProfileNumberAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_StandardEmployeeProfile_Invalid)
            .WithMessage("standardEmployeeProfile.employeeProfileNumber is invalid.");
    }

    /// <summary>
    /// Asynchronous validation method to check if the shiftNumber exists.
    /// </summary>
    /// <param name="patchModel">The YearBasePatchModel model.</param>
    /// <param name="token">The cancellation token.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a boolean indicating whether the shiftNumber is valid.</returns>
    private async Task<bool> BeValidShiftNumberAsync(YearWmPatchModel patchModel, CancellationToken token) =>
        patchModel.StandardShift is null ||
        await this.loketContext.Set<Repository.Entities.Shift>()
            .AnyAsync(sh =>
                    sh.ShiftId == patchModel.StandardShift.ShiftNumber &&
                    sh.Year == this.loketContext.Set<Repository.Entities.Year>()
                        .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Year>(patchModel.Id, null))
                        .Single(),
                token);

    /// <summary>
    /// Asynchronous validation method to check if the EmployeeProfileNumber exists.
    /// </summary>
    /// <param name="patchModel">The YearBasePatchModel model.</param>
    /// <param name="token">The cancellation token.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a boolean indicating whether the EmployeeProfileNumber is valid.</returns>
    private async Task<bool> BeValidEmployeeProfileNumberAsync(YearWmPatchModel patchModel, CancellationToken token) =>
        patchModel.StandardEmployeeProfile is null ||
        await this.loketContext.Set<EmployeeProfile>()
            .AnyAsync(ep =>
                    ep.EmployeeProfileId == patchModel.StandardEmployeeProfile.EmployeeProfileNumber &&
                    ep.Year == this.loketContext.Set<Repository.Entities.Year>()
                        .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Year>(patchModel.Id, null))
                        .Single(),
                token);
}