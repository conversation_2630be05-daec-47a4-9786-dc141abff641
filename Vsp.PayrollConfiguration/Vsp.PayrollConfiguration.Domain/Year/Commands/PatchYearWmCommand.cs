using Vsp.PayrollConfiguration.Domain.Year.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Year.Commands;

internal class PatchYearWmCommand(IBaseCommandDependencies<ILoketContext> dependencies, IGetInheritanceEntityQuery<YearClaWmModel, Repository.Entities.Year> query) :
    PatchYearCommand<YearWmPatchModel, YearClaWmModel>(dependencies, query);