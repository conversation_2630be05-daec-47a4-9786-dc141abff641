using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;

public class PayrollComponentPostModel : PayrollComponentPatchModel, IYearPostModel
{
    // Properties not in API but in DB
    [JsonIgnore]
    public int? GeneralLedgerAccountNumber { get; set; }
    [JsonIgnore]
    public int IsLifeSpanScheme { get; set; }
    [JsonIgnore]
    public int Order { get; set; }
    [JsonIgnore]
    public int IsVisibleByDefault { get; set; }
}