using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Authorizations;

public class PayrollComponentAuthorizationModel
{
    public Guid PayrollComponentId { get; set; }
}

public class PayrollComponentAuthorization(ILoketContext loketContext)
    : InheritanceEntityAuthorization<PayrollComponentAuthorizationModel, Component>(loketContext)
{
    protected override ResourceType ResourceType => ResourceType.Component;

    protected override Guid GetId(PayrollComponentAuthorizationModel authorizationObject) => authorizationObject.PayrollComponentId;
}