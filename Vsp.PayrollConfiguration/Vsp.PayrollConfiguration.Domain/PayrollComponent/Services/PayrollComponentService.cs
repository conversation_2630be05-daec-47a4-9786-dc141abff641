using Vsp.PayrollConfiguration.Domain.PayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Services;

public class PayrollComponentService(
    ILoketContext loketContext,
    IMapper mapper,
    IFilteredQuery<PayrollComponentModel, Component, Repository.Entities.Year, ILoketContext> getPayrollComponentsQuery,
    IFilteredQuery<PayrollComponentMinimizedModel, Component, Repository.Entities.Year, ILoketContext> getPayrollComponentsMinimizedQuery,
    IFilteredQuery<PayrollComponentMinimizedModel, ComponentGeneral, Repository.Entities.Year, ILoketContext> getAvailablePayrollComponentsQuery,
    IInsertInheritanceEntityCommand<PayrollComponentPostModel, PayrollComponentModel, Component, ModelComponent> insertCommand,
    IPatchInheritanceEntityCommand<PayrollComponentPatchModel, PayrollComponentModel, Component, ModelComponent> patchCommand,
    IDeleteInheritanceEntityCommand<ModelComponent> deleteCommand,
    ICodeTableHelper codeTableHelper)
    : IPayrollComponentService
{
    private readonly ILoketContext loketContext = loketContext;
    private readonly IMapper mapper = mapper;
    private readonly IFilteredQuery<PayrollComponentModel, Component, Repository.Entities.Year, ILoketContext> getPayrollComponentsQuery = getPayrollComponentsQuery;
    private readonly IFilteredQuery<PayrollComponentMinimizedModel, Component, Repository.Entities.Year, ILoketContext> getPayrollComponentsMinimizedQuery = getPayrollComponentsMinimizedQuery;
    private readonly IFilteredQuery<PayrollComponentMinimizedModel, ComponentGeneral, Repository.Entities.Year, ILoketContext> getAvailablePayrollComponentsQuery = getAvailablePayrollComponentsQuery;
    private readonly IInsertInheritanceEntityCommand<PayrollComponentPostModel, PayrollComponentModel, Component, ModelComponent> insertCommand = insertCommand;
    private readonly IPatchInheritanceEntityCommand<PayrollComponentPatchModel, PayrollComponentModel, Component, ModelComponent> patchCommand = patchCommand;
    private readonly IDeleteInheritanceEntityCommand<ModelComponent> deleteCommand = deleteCommand;
    private readonly ICodeTableHelper codeTableHelper = codeTableHelper;

    public async Task<IListOperationResult<PayrollComponentModel>> GetPayrollComponentsByYearIdAsync(Guid yearId)
        => await this.getPayrollComponentsQuery.ExecuteList(yearId);

    public async Task<IListOperationResult<PayrollComponentMinimizedModel>> GetPayrollComponentsMinimizedByYearIdAsync(Guid yearId)
        => await this.getPayrollComponentsMinimizedQuery.ExecuteList(yearId);

    public async Task<IListOperationResult<PayrollComponentMinimizedModel>> GetAvailablePayrollComponentsByYearIdAsync(Guid yearId)
        => await this.getAvailablePayrollComponentsQuery.ExecuteList(yearId);

    public async Task<IOperationResult<PayrollComponentModel>> PostPayrollComponentByInheritanceLevelIdAsync(Guid inheritanceLevelId, PayrollComponentExternalPostModel postModel, bool isAddArrangementPayrollComponents)
    {
        var model = this.mapper.Map<PayrollComponentPostModel>(postModel);
        model.InheritanceLevelId = inheritanceLevelId;

        var componentGeneral = await this.loketContext.Set<ComponentGeneral>()
            .AsNoTracking()
            .SingleOrDefaultAsync(cg => cg.ComponentId == postModel.Key);

        // If the componentGeneral was not found, the specified input model key is invalid,
        // and we should bypass this mapping and let the command do the validation
        if (componentGeneral != null)
        {
            // Enrich POST model
            this.mapper.Map(componentGeneral, model);

            // Apply post-processing to set the Column based on Key (plus extra things)
            model.ApplyPostProcessing(componentGeneral);
        }
        else
        {
            // If the componentGeneral was not found, mark the Key as -1 to bypass PATCH validation
            model.Key = -1;
        }

        this.insertCommand.IsAddArrangementPayrollComponentsChildCommand = isAddArrangementPayrollComponents;
        return await this.insertCommand.ExecuteAsync(model);
    }

    public async Task<IOperationResult<PayrollComponentModel>> PatchPayrollComponentByPayrollComponentIdAsync(Guid payrollComponentId, PayrollComponentPatchModel patchModel)
    {
        patchModel.Id = payrollComponentId;

        var componentGeneral = await this.loketContext.Set<ComponentGeneral>()
            .AsNoTracking()
            .SingleAsync(cg => cg.ComponentId == patchModel.Key);

        // Apply post-processing to set the Column based on Key (plus extra things)
        patchModel.ApplyPostProcessing(componentGeneral);

        return await this.patchCommand.ExecuteAsync(patchModel);
    }

    public async Task<IOperationResult<NoResult>> DeletePayrollComponentByPayrollComponentIdAsync(Guid payrollComponentId)
        => await this.deleteCommand.ExecuteAsync(payrollComponentId);

    public async Task<IOperationResult<PayrollComponentMetadataModel>> GetPayrollComponentMetadataByProviderIdAsync()
    {
        var codeTableTasks = new[]
        {
            this.codeTableHelper.GetOptions<CtDeductionOrPayment>(),
            this.codeTableHelper.GetOptions<CtPaymentPeriod>(),
            this.codeTableHelper.GetOptions<CtTaxLiable>(),
            this.codeTableHelper.GetOptions<CtSocialSecurityLiable>(),
            this.codeTableHelper.GetOptions<CtHoursIndication>(),
            this.codeTableHelper.GetOptions<CtCostsEmployer>(),
            this.codeTableHelper.GetOptions<CtBaseForCalculationBter>(),
            this.codeTableHelper.GetOptions<CtColumn>(),
            this.codeTableHelper.GetOptions<CtBalanceSheetSide>(),
            this.codeTableHelper.GetOptions<CtCategory>()
        };

        await Task.WhenAll(codeTableTasks);

        var metadata = new PayrollComponentMetadataModel
        {
            DeductionOrPayment = codeTableTasks[0].Result,
            PaymentPeriod = codeTableTasks[1].Result,
            TaxLiable = codeTableTasks[2].Result,
            SocialSecurityLiable = codeTableTasks[3].Result,
            HoursIndication = codeTableTasks[4].Result,
            CostsEmployer = codeTableTasks[5].Result,
            BaseForCalculationBter = codeTableTasks[6].Result,
            Column = codeTableTasks[7].Result,
            BalanceSheetSide = codeTableTasks[8].Result,
            Category = codeTableTasks[9].Result
        };
        return new OperationResult<PayrollComponentMetadataModel>(metadata);
    }
}