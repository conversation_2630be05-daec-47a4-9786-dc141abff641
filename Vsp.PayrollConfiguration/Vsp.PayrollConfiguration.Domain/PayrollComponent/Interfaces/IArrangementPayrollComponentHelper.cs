using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Interfaces;

public interface IArrangementPayrollComponentHelper
{
    Task<IList<OperationMessage>> AddArrangementPayrollComponentsAsync(ArrangementType arrangementType, IArrangementPostModel arrangementPostModel, IYearEntity arrangementEntity);
}