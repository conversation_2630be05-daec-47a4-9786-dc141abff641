using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Interfaces;
using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Services;

public class PayrollComponentExtraService(ILoketContext dbContext, IMapper mapper, ICodeTableHelper codeTableHelper,
    IGetByIdQuery<PayrollComponentExtraModel, Component, ILoketContext> query,
    IPatchPayrollComponentExtraCommand patchCommand)
    : IPayrollComponentExtraService
{
    private readonly ILoketContext dbContext = dbContext;
    private readonly IMapper mapper = mapper;
    private readonly ICodeTableHelper codeTableHelper = codeTableHelper;
    private readonly IGetByIdQuery<PayrollComponentExtraModel, Component, ILoketContext> query = query;
    private readonly IPatchPayrollComponentExtraCommand patchCommand = patchCommand;

    public async Task<IOperationResult<PayrollComponentExtraModel>> GetPayrollComponentExtraByPayrollComponentIdAsync(Guid payrollComponentId) =>
        await this.query.Execute(payrollComponentId);

    public async Task<IOperationResult<PayrollComponentExtraMetadataModel>> GetPayrollComponentExtraMetadataByPayrollComponentIdAsync(Guid payrollComponentId)
    {
        var category = await this.dbContext.Set<Component>()
            .Where(GeneratedIdHelper.ConstructWhere<Component>(payrollComponentId))
            .Select(c => c.Category)
            .SingleAsync();
        var model = category != 6
            ? new PayrollComponentExtraMetadataModel
            {
                RouteType = null
            }
            : new PayrollComponentExtraMetadataModel
            {
                RouteType = await this.codeTableHelper.GetOptions<CtRouteType>()
            };

        return new OperationResult<PayrollComponentExtraMetadataModel>(model);
    }

    public async Task<IOperationResult<PayrollComponentExtraModel>> PatchPayrollComponentExtraByPayrollComponentIdAsync(
        Guid payrollComponentId,
        PayrollComponentExtraPatchModel patchModel)
    {
        patchModel.PayrollComponentId = payrollComponentId;
        return await this.patchCommand.Execute(patchModel);
    }
}