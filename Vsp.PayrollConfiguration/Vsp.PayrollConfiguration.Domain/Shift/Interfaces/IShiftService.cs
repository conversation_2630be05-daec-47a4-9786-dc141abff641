using Vsp.PayrollConfiguration.Domain.Shift.Models;

namespace Vsp.PayrollConfiguration.Domain.Shift.Interfaces;

public interface IShiftService
{
    Task<IListOperationResult<ShiftModel>> GetShiftsByYearIdAsync(Guid yearId);
    Task<IOperationResult<ShiftModel>> PostShiftByInheritanceLevelIdAsync(Guid inheritanceLevelId, ShiftPostModel postModel);
    Task<IOperationResult<ShiftModel>> PatchShiftByShiftIdAsync(Guid shiftId, ShiftPatchModel patchModel);
    Task<IOperationResult<NoResult>> DeleteShiftByShiftIdAsync(Guid shiftId);
}
