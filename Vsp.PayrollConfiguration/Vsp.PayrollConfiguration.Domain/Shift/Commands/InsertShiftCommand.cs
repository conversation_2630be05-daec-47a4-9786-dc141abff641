using Vsp.PayrollConfiguration.Domain.Shift.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Shift.Commands;

internal class InsertShiftCommand(
    IInsertCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<ShiftModel, Repository.Entities.Shift> query,
    IServiceProvider serviceProvider)
    : InsertInheritanceEntityCommand<ShiftPostModel, ShiftModel, Repository.Entities.Shift, ModelShift>(dependencies, query, serviceProvider)
{
    protected override bool AddFutureYears => true;
}