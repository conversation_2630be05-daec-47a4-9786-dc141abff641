using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Shift.Validators;

internal class DeleteShiftValidator : AbstractValidator<ModelShift>
{
    public DeleteShiftValidator(ILoketContext loketContext, IMapper mapper) =>
        Include(new DeleteInheritanceEntityValidator<ModelShift>(loketContext, mapper));
}