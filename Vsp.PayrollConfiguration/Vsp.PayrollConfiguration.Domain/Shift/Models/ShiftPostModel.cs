using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.Domain.Shift.Models;

public class ShiftPostModel : ShiftPatchModel, IPayrollPeriodPostModel
{
    [JsonIgnore]
    public Guid InheritanceLevelId { get; set; }

    /// <summary>
    /// Shift number.
    /// </summary>
    /// <example>1</example>
    [Required]
    [Range(1, 15)]
    public int? ShiftNumber { get; set; } = null!;
    
    /// <summary>
    /// The year number.
    /// </summary>
    /// <example>2025</example>
    [Required]
    [Range(1900, 9999)]
    public int? Year { get; set; } = null!;

    [Required]
    public PayrollPeriodNumberModel StartPayrollPeriod { get; set; } = null!;
}