using Vsp.PayrollConfiguration.Domain.Shift.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Shift.Mappers;

internal class ShiftProfile : Profile
{
    public ShiftProfile()
    {
        // GET
        CreateMap<Repository.Entities.Shift, ShiftModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.ShiftNumber, opt => opt.MapFrom(src => src.ShiftId))
            .ForMember(dst => dst.StartPayrollPeriod, opt => opt.MapFrom(src => src.PayrollPeriod))
            .ForMember(dst => dst.FullTimeHoursPerWeek, opt => opt.MapFrom(src => src.FullTimeHoursPerWeek))
            .ForMember(dst => dst.BonusPercentage, opt => opt.MapFrom(src => src.BonusPercentage))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId));

        CreateMap<Repository.Entities.Shift, ShiftDefinedAtLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.ShiftIdDefinedAtLevel))
            .ForMember(dst => dst.FullTimeHoursPerWeek, opt => opt.MapFrom(src => src.FullTimeHoursPerWeekDefinedAtLevel))
            .ForMember(dst => dst.BonusPercentage, opt => opt.MapFrom(src => src.BonusPercentageDefinedAtLevel));

        // POST
        CreateMap<ShiftPostModel, ModelShift>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom((_, _, _, context) => context.TryGetItems(out var items) ? (int)items[nameof(IInheritanceEntity.InheritanceLevelId)] : default))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.Year))
            .ForMember(dst => dst.ShiftId, opt => opt.MapFrom(src => src.ShiftNumber))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.StartPayrollPeriod.PeriodNumber))
            .ForMember(dst => dst.FullTimeHoursPerWeek, opt => opt.MapFrom(src => src.FullTimeHoursPerWeek))
            .ForMember(dst => dst.BonusPercentage, opt => opt.MapFrom(src => src.BonusPercentage))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore());

        // For cloning objects with AutoMapper
        CreateMap<ModelShift, ModelShift>()
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore());
        CreateMap<ShiftPostModel, ShiftPostModel>();

        // PATCH
        CreateMap<ShiftPatchModel, Repository.Entities.Shift>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.ShiftId, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.Ignore())
            .ForMember(dst => dst.ShiftIdDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.FullTimeHoursPerWeek, opt => opt.MapFrom(src => src.FullTimeHoursPerWeek))
            .ForMember(dst => dst.FullTimeHoursPerWeekDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.BonusPercentage, opt => opt.MapFrom(src => src.BonusPercentage))
            .ForMember(dst => dst.BonusPercentageDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore());

        CreateMap<Repository.Entities.Shift, ModelShift>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom(src => src.InheritanceLevelId))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.ShiftId, opt => opt.MapFrom(src => src.ShiftId))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.PayrollPeriodId))
            .ForMember(dst => dst.FullTimeHoursPerWeek, opt => opt.MapFrom(src => src.FullTimeHoursPerWeek))
            .ForMember(dst => dst.BonusPercentage, opt => opt.MapFrom(src => src.BonusPercentage))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore());
    }
}