using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Authorizations;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Commands;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Queries;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Services;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Validators;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<BaseForCalculationAgeBasedMinimumAuthorizationModel>, BaseForCalculationAgeBasedMinimumAuthorization>();

        services.AddScoped<IBaseForCalculationAgeBasedMinimumService, BaseForCalculationAgeBasedMinimumService>();

        services.AddScoped<IValidator<ModelBaseForCalculationAgeBasedMinimum>, DeleteBaseForCalculationAgeBasedMinimumValidator>();

        services.AddScoped<IGetInheritanceEntityQuery<BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum>, GetBaseForCalculationAgeBasedMinimumQuery>();

        services.AddScoped<IDeleteInheritanceEntityCommand<ModelBaseForCalculationAgeBasedMinimum>, DeleteBaseForCalculationAgeBasedMinimumCommand>();
        services.AddScoped<IInsertInheritanceEntityCommand<BaseForCalculationAgeBasedMinimumPostModel, BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum, ModelBaseForCalculationAgeBasedMinimum>, InsertBaseForCalculationAgeBasedMinimumCommand>();
        services.AddScoped<IPatchInheritanceEntityCommand<BaseForCalculationAgeBasedMinimumPatchModel, BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum, ModelBaseForCalculationAgeBasedMinimum>, PatchBaseForCalculationAgeBasedMinimumCommand>();
    }
}