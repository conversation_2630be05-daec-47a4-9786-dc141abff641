using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Validators;

public class DeleteBaseForCalculationAgeBasedMinimumValidator : AbstractValidator<ModelBaseForCalculationAgeBasedMinimum>
{
    public DeleteBaseForCalculationAgeBasedMinimumValidator(ILoketContext loketContext, IMapper mapper) =>
        Include(new DeleteInheritanceEntityValidator<ModelBaseForCalculationAgeBasedMinimum>(loketContext, mapper));
}