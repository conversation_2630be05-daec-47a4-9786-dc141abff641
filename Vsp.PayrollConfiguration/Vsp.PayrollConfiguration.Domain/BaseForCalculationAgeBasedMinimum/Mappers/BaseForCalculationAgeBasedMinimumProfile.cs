using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Mappers;

public class BaseForCalculationAgeBasedMinimumProfile : Profile
{
    public BaseForCalculationAgeBasedMinimumProfile()
    {
        #region GET

        CreateMap<Repository.Entities.BaseForCalculationAgeBasedMinimum, BaseForCalculationAgeBasedMinimumModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.BaseForCalculation, opt => opt.MapFrom(src => src.BaseForCalculationId))
            .ForMember(dst => dst.Age, opt => opt.MapFrom(src => src.Age))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.StartPayrollPeriod, opt => opt.MapFrom(src => src.PayrollPeriod))
            .ForMember(dst => dst.Minimum, opt => opt.MapFrom(src => src.Minimum))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src));

        CreateMap<Repository.Entities.BaseForCalculationAgeBasedMinimum, BaseForCalculationAgeBasedMinimumDefinedAtLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.AgeDefinedAtLevel))
            .ForMember(dst => dst.Minimum, opt => opt.MapFrom(src => src.MinimumDefinedAtLevel));

        #endregion

        // For cloning objects with AutoMapper
        CreateMap<ModelBaseForCalculationAgeBasedMinimum, ModelBaseForCalculationAgeBasedMinimum>()
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore());           
    }
}