using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;
using Vsp.PayrollConfiguration.Infrastructure.Queries;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Queries;

internal class GetBaseForCalculationAgeBasedMinimumQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : GetInheritanceEntityQuery<BaseForCalculationAgeBasedMinimumModel, Repository.Entities.BaseForCalculationAgeBasedMinimum>(dependencies)
{
    public override Expression<Func<Repository.Entities.BaseForCalculationAgeBasedMinimum, bool>>? FilterCollectionByExpression(Guid baseForCalculationId)
    {
        var baseForCalculation = new Repository.Entities.BaseForCalculation { Id = baseForCalculationId };
        GeneratedIdHelper.GenerateIdKeys(baseForCalculation);

        return x =>
            x.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
            x.YearId == baseForCalculation.YearId &&
            x.BaseForCalculationId == baseForCalculation.BaseForCalculationId;
    }
}