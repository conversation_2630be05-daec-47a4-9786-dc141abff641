using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Authorizations;

public class BaseForCalculationAgeBasedMinimumAuthorizationModel
{
    public Guid BaseForCalculationAgeBasedMinimumId { get; set; }
}

public class BaseForCalculationAgeBasedMinimumAuthorization(ILoketContext loketContext)
    : InheritanceEntityAuthorization<BaseForCalculationAgeBasedMinimumAuthorizationModel, Repository.Entities.BaseForCalculationAgeBasedMinimum>(loketContext)
{
    protected override ResourceType ResourceType => ResourceType.GrondslagMinimum;

    protected override Guid GetId(BaseForCalculationAgeBasedMinimumAuthorizationModel authorizationObject) =>
        authorizationObject.BaseForCalculationAgeBasedMinimumId;
}