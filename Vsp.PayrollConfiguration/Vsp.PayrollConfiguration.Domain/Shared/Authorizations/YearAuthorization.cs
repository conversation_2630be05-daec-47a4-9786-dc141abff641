using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Shared.Authorizations;

public class YearAuthorizationModel
{
    public Guid YearId { get; set; }
}

public class YearAuthorization(ILoketContext loketContext) : InheritanceEntityAuthorization<YearAuthorizationModel, Repository.Entities.Year>(loketContext)
{
    protected override ResourceType ResourceType => ResourceType.Jaar;

    protected override Guid GetId(YearAuthorizationModel authorizationObject) => authorizationObject.YearId;
}
