namespace Vsp.PayrollConfiguration.Domain.Shared.Models;

/// <summary>
/// Year.
/// </summary>
public class YearMinimizedModel
{
    /// <summary>
    /// The unique identifier of a year (GUID/UUID).
    /// </summary>
    /// <example>123e4567-e89b-12d3-a456-************</example>
    public Guid Id { get; set; }
    public InheritanceLevelModel InheritanceLevel { get; set; } = null!;

    /// <summary>
    /// Year.
    /// </summary>
    /// <example>2025</example>
    public int Year { get; set; }

    /// <summary>
    /// Indicates the frequence of the payrolling process. Payrolling can be done on a montly, 4-weekly or weekly basis.
    ///    - 1: <c>Maand</c> (monthly)
    ///    - 3: <c>4 Weken</c> (4-weekly)
    ///    - 4: <c>Week</c> (weekly)
    /// </summary>
    public KeyValueModel PayrollPeriodType { get; set; } = null!;
}
