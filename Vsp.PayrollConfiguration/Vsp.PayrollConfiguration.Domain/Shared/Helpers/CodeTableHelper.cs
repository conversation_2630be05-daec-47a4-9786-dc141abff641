using LazyCache;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Shared.Helpers;

public class CodeTableHelper(IServiceScopeFactory serviceScopeFactory, IMapper mapper) : ICodeTableHelper
{
    private readonly IServiceScopeFactory serviceScopeFactory = serviceScopeFactory;
    private readonly IMapper mapper = mapper;

    public async Task<List<KeyValueModel>> GetOptions<TEntity>() where TEntity : CodeTable
    {
        using var scope = this.serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ILoketContext>();
        var query = context.Set<TEntity>()
            .OrderBy(x => x.Order);

        var codeTableItems = (await CodeTableCache.WhereAsync(query)).GetCodeTableList();

        var excludeCodeZero = typeof(CodeTableNullable).IsAssignableFrom(typeof(TEntity));

        return codeTableItems
            .Where(x => !excludeCodeZero || x.Code != 0)
            .Select(this.mapper.Map<KeyValueModel>)
            .ToList();
    }

    /// <summary>
    /// Use this class to cache calls to code tables.
    /// </summary>
    /// <remarks>
    /// TODO: remove this class copy in .NET9 upgrade and use <c>Entities.EntityFramework</c> package instead
    /// </remarks>
    public class CodeTableCache
    {
        private static readonly IAppCache codeTableCache = CreateCache();

        private readonly IEnumerable<CodeTable> currentList;

        private CodeTableCache(IEnumerable<CodeTable> currentList) => this.currentList = currentList;

        /// <summary>
        /// Initialize a cached version of a code table. If not present in the cache the IQueryable will be materialized.
        /// </summary>
        /// <typeparam name="TEntity">Valid CodeTable implementation</typeparam>
        /// <param name="entities">Valid IQueryable of CodeTable</param>
        /// <returns>An instance of the CodeTableCache scoped to the matching entity CodeTable</returns>
        public static CodeTableCache Where<TEntity>(IQueryable<TEntity> entities) where TEntity : CodeTable
        {
            var list = codeTableCache.GetOrAdd($"CodeTableCache_Where_{typeof(TEntity)}", () =>
                entities.ToList().OfType<CodeTable>(), TimeSpan.FromHours(24));

            return new CodeTableCache(list);
        }

        /// <summary>
        /// Initialize a cached version of a code table. If not present in the cache the IQueryable will be materialized.
        /// </summary>
        /// <typeparam name="TEntity">Valid CodeTable implementation</typeparam>
        /// <param name="entities">Valid IQueryable of CodeTable</param>
        /// <returns>An instance of the CodeTableCache scoped to the matching entity CodeTable</returns>
        public static async Task<CodeTableCache> WhereAsync<TEntity>(IQueryable<TEntity> entities) where TEntity : CodeTable
        {
            var list = await codeTableCache.GetOrAddAsync($"CodeTableCache_Where_{typeof(TEntity)}", async () =>
                (await entities.ToListAsync()).OfType<CodeTable>(), TimeSpan.FromHours(24));

            return new CodeTableCache(list);
        }

        /// <summary>
        /// Determines if a CodeTable in the current scope contains the requested value.
        /// </summary>
        /// <param name="value">Integer value correspondinhg with the code of the CodeTable value.</param>
        /// <returns>True if the current CodeTable contains this code value</returns>
        public bool Contains(int value) =>
            this.currentList.Any(x => x.Code == value);

        /// <summary>
        /// Get a description from the CodeTable in the current scope with the requested value.
        /// </summary>
        /// <param name="value">Integer value corresponding with the code of the CodeTable value.</param>
        /// <returns>The description, if present. Null, if not present</returns>
        public string? GetDescription(int? value) =>
            value != null
                ? this.currentList.FirstOrDefault(x => x.Code == value)?.Omschrijving
                : default;

        /// <summary>
        /// Get a description from the CodeTable in the current scope with the requested value.
        /// </summary>
        /// <param name="value">Integer value corresponding with the code of the CodeTable value.</param>
        /// <returns>The CodeTable, if present. Null, if not present</returns>
        public CodeTable? GetCodeTable(int? value) =>
            value != null
                ? this.currentList.FirstOrDefault(x => x.Code == value)
                : default;

        public IEnumerable<CodeTable> GetCodeTableList() => this.currentList;

        private static CachingService CreateCache() => new() { DefaultCachePolicy = { DefaultCacheDurationSeconds = 300 } };
    }
}