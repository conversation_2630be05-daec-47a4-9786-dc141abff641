using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;
using Vsp.PayrollConfiguration.Domain.Shared.Helpers;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Shared;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<UserAuthorizationModel>, UserAuthorization>();
        services.AddScoped<IAuthorizeApiProtocol<ProviderAuthorizationModel>, ProviderAuthorization>();

        services.AddScoped<ICodeTableHelper, CodeTableHelper>();
    }
}