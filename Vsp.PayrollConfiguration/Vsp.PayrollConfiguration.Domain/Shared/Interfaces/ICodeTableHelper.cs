using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;

namespace Vsp.PayrollConfiguration.Domain.Shared.Interfaces;

public interface ICodeTableHelper
{
    /// <summary>
    /// Retrieves a list of options for a specific code table entity, projecting the results to <see cref="KeyValueModel"/>.
    /// </summary>
    /// <typeparam name="TEntity">
    /// The type of the code table entity. Must inherit from <see cref="CodeTable"/>.
    /// </typeparam>
    Task<List<KeyValueModel>> GetOptions<TEntity>() where TEntity : CodeTable;
}
