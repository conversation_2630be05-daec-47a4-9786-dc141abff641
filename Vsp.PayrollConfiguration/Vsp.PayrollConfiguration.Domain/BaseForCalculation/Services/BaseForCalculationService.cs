using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculation.Services;

public class BaseForCalculationService(
    IFilteredQuery<BaseForCalculationModel, Repository.Entities.BaseForCalculation, Repository.Entities.Year, ILoketContext> getQuery,
    IGetLinkedPayrollComponentsByBaseForCalculationIdQuery getLinkedPayrollComponentsQuery,
    IInsertInheritanceEntityCommand<BaseForCalculationPostModel, BaseForCalculationModel, Repository.Entities.BaseForCalculation, ModelBaseForCalculation> insertCommand,
    IPatchInheritanceEntityCommand<BaseForCalculationPatchModel, BaseForCalculationModel, Repository.Entities.BaseForCalculation, ModelBaseForCalculation> patchCommand,
    IDeleteInheritanceEntityCommand<ModelBaseForCalculation> deleteCommand,
    ICodeTableHelper codeTableHelper)
    : IBaseForCalculationService

{
    private readonly IFilteredQuery<BaseForCalculationModel, Repository.Entities.BaseForCalculation, Repository.Entities.Year, ILoketContext> getQuery = getQuery;
    private readonly IGetLinkedPayrollComponentsByBaseForCalculationIdQuery getLinkedPayrollComponentsQuery = getLinkedPayrollComponentsQuery;
    private readonly IInsertInheritanceEntityCommand<BaseForCalculationPostModel, BaseForCalculationModel, Repository.Entities.BaseForCalculation, ModelBaseForCalculation> insertCommand = insertCommand;
    private readonly IPatchInheritanceEntityCommand<BaseForCalculationPatchModel, BaseForCalculationModel, Repository.Entities.BaseForCalculation, ModelBaseForCalculation> patchCommand = patchCommand;
    private readonly IDeleteInheritanceEntityCommand<ModelBaseForCalculation> deleteCommand = deleteCommand;
    private readonly ICodeTableHelper codeTableHelper = codeTableHelper;

    public async Task<IListOperationResult<BaseForCalculationModel>> GetBasesForCalculationByYearIdAsync(Guid yearId) =>
        await this.getQuery.ExecuteList(yearId);

    public async Task<IOperationResult<BaseForCalculationModel>> PostBaseForCalculationByInheritanceLevelIdAsync(Guid inheritanceLevelId, BaseForCalculationPostModel postModel)
    {
        postModel.InheritanceLevelId = inheritanceLevelId;
        return await this.insertCommand.ExecuteAsync(postModel);
    }

    public async Task<IOperationResult<BaseForCalculationModel>> PatchBaseForCalculationByBaseForCalculationIdAsync(Guid baseForCalculationId, BaseForCalculationPatchModel patchModel)
    {
        patchModel.Id = baseForCalculationId;
        return await this.patchCommand.ExecuteAsync(patchModel);
    }

    public async Task<IOperationResult<NoResult>> DeleteBaseForCalculationByBaseForCalculationIdAsync(Guid baseForCalculationId) =>
        await this.deleteCommand.ExecuteAsync(baseForCalculationId);

    public async Task<IOperationResult<BaseForCalculationMetadataModel>> GetBaseForCalculationMetadataByProviderIdAsync()
    {
        var codeTableTasks = new[]
        {
            this.codeTableHelper.GetOptions<CtBaseType>(),
            this.codeTableHelper.GetOptions<CtEmployeeAgeType>(),
            this.codeTableHelper.GetOptions<CtPayslipType>(),
            this.codeTableHelper.GetOptions<CtMinimumMaximumType>()
        };

        await Task.WhenAll(codeTableTasks);

        var metadata = new BaseForCalculationMetadataModel
        {
            BaseType = codeTableTasks[0].Result,
            EmployeeAgeType = codeTableTasks[1].Result,
            PayslipType = codeTableTasks[2].Result,
            MinimumMaximumType = codeTableTasks[3].Result
        };

        return new OperationResult<BaseForCalculationMetadataModel>(metadata);
    }

    public async Task<IListOperationResult<PayrollComponentModel>> GetLinkedPayrollComponentsByBaseForCalculationIdAsync(Guid baseForCalculationId) =>
        await this.getLinkedPayrollComponentsQuery.ExecuteList(baseForCalculationId);
}