using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Converters;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;

public class BaseForCalculationPatchModel : IPatchModel
{
    [JsonIgnore] 
    public Guid Id { get; set; }

    [Required, MaxLength(50)]
    [JsonConverter(typeof(TrimStringConverter))]
    public string Description { get; set; } = null!;
    
    [JsonProperty(Required = Required.AllowNull)]
    public KeyModel? BaseType { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public KeyModel? StartEmployeeAgeType { get; set; }

    [Required]
    [Range(0, 999.75, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^\d{1,3}([.,](00|25|50|75))?$", ErrorMessage = "'{0}' only allows .00/.25/.50/.75 fractions")]
    [JsonConverter(typeof(RoundingDecimalConverter), 2)]
    public decimal? StartEmployeeAge { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public KeyModel? EndEmployeeAgeType { get; set; }
    
    [Required]
    [Range(0, 999.75, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^\d{1,3}(\.(00|25|50|75))?$", ErrorMessage = "'{0}' only allows .00/.25/.50/.75 fractions")]
    [JsonConverter(typeof(RoundingDecimalConverter), 2)]
    public decimal? EndEmployeeAge { get; set; }
    
    [Required]
    public KeyModel ResultPayrollComponent { get; set; } = null!;
    
    [Required]
    [Range(0, 999.999999, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,6})?$", ErrorMessage = "{0} must have at most 6 decimals.")]
    public decimal? Percentage { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public PayrollPeriodNumberModel? CalculationPayrollPeriod { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public PayrollPeriodNumberModel? ReferencePayrollPeriod { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public PayrollPeriodNumberModel? PayoutPayrollPeriod { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public PayrollPeriodNumberModel? AccrualEndPayrollPeriod { get; set; }
    
    [Required]
    public KeyModel PayslipType { get; set; } = null!;
    
    [Required]
    public bool? IsPayoutAtStartOfEmployment { get; set; }
    
    [Required]
    public bool? IsPayoutAtEndOfEmployment { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public KeyModel? AdvancePayrollComponent { get; set; }
    
    [Required]
    [Range(0, 999.999999, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,6})?$", ErrorMessage = "{0} must have at most 6 decimals.")]
    public decimal? AdvancePercentage { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public PayrollPeriodNumberModel? AdvancePayrollPeriod { get; set; }
    
    [JsonProperty(Required = Required.AllowNull)]
    public KeyModel? PeriodicReservationPayrollComponent { get; set; }
    
    [Required]
    [Range(0, 999.999999, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,6})?$", ErrorMessage = "{0} must have at most 6 decimals.")]
    public decimal? FinancialReservationPercentage { get; set; }
    
    [Required]
    [Range(0, 999.999999, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,6})?$", ErrorMessage = "{0} must have at most 6 decimals.")]
    public decimal? FinancialMarkupPercentage { get; set; }
    
    [Required]
    public bool? IsCumulativeCalculation { get; set; }
    
    [Required]
    public bool? IsPartTimeCalculation { get; set; }
    
    [Required]
    public bool? IsAutomaticCalculation { get; set; }
    
    [Required]
    public bool? IsSupplementingDailyWage { get; set; }
    
    [Required]
    public KeyModel MinimumMaximumType { get; set; } = null!;
}