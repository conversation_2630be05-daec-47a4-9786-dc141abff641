using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Validators;

public class DeleteDifferentiatedReturnToWorkFundValidator : AbstractValidator<Repository.Entities.DifferentiatedReturnToWorkFund>
{
    private readonly ILoketContext loketContext;

    public DeleteDifferentiatedReturnToWorkFundValidator(ILoketContext loketContext)
    {
        this.loketContext = loketContext;

        RuleFor(x => x)
            .MustAsync(FirstPayrollPeriodAlwaysExistsAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted)
            .WithMessage("Cannot delete data for the first payroll period in this year, because there is still data for later payroll period(s).");
    }

    private async Task<bool> FirstPayrollPeriodAlwaysExistsAsync(Repository.Entities.DifferentiatedReturnToWorkFund entity, CancellationToken token)
    {
        // Entities after the first payroll period may always be deleted.
        if (entity.PayrollPeriodId > 1) return true;

        // If there are no other entities after the first payroll period on the current level, the first payroll period may always be deleted.
        var afterFirstPayrollPeriodEntitiesExistOnCurrentLevel = await this.loketContext.Set<Repository.Entities.DifferentiatedReturnToWorkFund>()
            .AnyAsync(x => x.InheritanceLevelId == entity.InheritanceLevelId && x.YearId == entity.YearId && x.PayrollPeriodId > 1, token);

        return !afterFirstPayrollPeriodEntitiesExistOnCurrentLevel;
    }
}