using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.UnitTest;

internal static class MockedDataHelper
{
    #region Users
    public static User[] Users =
    [
        // Users for Provider 1
        new User
        {
            Id = Guid.NewGuid(),
            ProviderId = 1,
            UserId = 1
        },
        // Users for Provider 2
        new User
        {
            Id = Guid.NewGuid(),
            ProviderId = 2,
            UserId = 2
        },
        new User
        {
            Id = Guid.NewGuid(),
            ProviderId = 2,
            UserId = 3
        },
        // Users for Provider 3
        new User
        {
            Id = Guid.NewGuid(),
            ProviderId = 3,
            UserId = 4
        },
        new User
        {
            Id = Guid.NewGuid(),
            ProviderId = 3,
            UserId = 5
        },
        new User
        {
            Id = Guid.NewGuid(),
            ProviderId = 3,
            UserId = 6
        },
    ];
    #endregion

    #region Providers
    public static Provider[] Providers =
    [
        // Provider 1
        new Provider
        {
            Id = Guid.NewGuid(),
            ProviderId = 1,
            Name = "Provider01",
            Users = [
                Users[0]
            ]
        },
        // Provider 2
        new Provider
        {
            Id = Guid.NewGuid(),
            ProviderId = 2,
            Name = "Provider02",
            Users = [
                Users[1], Users[2]
            ]
        },
        // Provider 3
        new Provider
        {
            Id = Guid.NewGuid(),
            ProviderId = 3,
            Name = "Provider03",
            Users = [
                Users[3], Users[4], Users[5]
            ]
        }
    ];
    #endregion

    #region CollectiveLaborAgreements
    // To make the testdata more intuitive we use InheritanceLevelId's in the 10 to 99 range
    public static Repository.Entities.Base.InheritanceLevel[] CollectiveLaborAgreements =
    [
        // Collective Labor Agreements for Provider 1
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 10,
            ParentInheritanceLevelId = 0,
            ProviderId = 0,
            Description = "18 Visdetailhandel",
            Comment = "Whatever 0",
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 10,
                Type = (int)InheritanceLevel.CollectiveLaborAgreement
            },
            ParentInheritanceLevel = null,
            Provider = null,
            Administration = null,
        },
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 11,
            ParentInheritanceLevelId = 0,
            ProviderId = 0,
            Description = "CAO_01",
            Comment = "Whatever 1",
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 11,
                Type = (int)InheritanceLevel.CollectiveLaborAgreement
            },
            ParentInheritanceLevel = null,
            Provider = null,
            Administration = null,
        },
        // Collective Labor Agreements for Provider 2
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 20,
            ParentInheritanceLevelId = 0,
            ProviderId = 0,
            Description = "CLA_20",
            Comment = "Collective Labor Agreement for Provider2",
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 20,
                Type = (int)InheritanceLevel.CollectiveLaborAgreement
            },
            ParentInheritanceLevel = null,
            Provider = null,
            Administration = null,
        },
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 21,
            ParentInheritanceLevelId = 0,
            ProviderId = 0,
            Description = "CAO_21",
            Comment = "Collective Labor Agreement for Provider2",
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 21,
                Type = (int)InheritanceLevel.CollectiveLaborAgreement
            },
            ParentInheritanceLevel = null,
            Provider = null,
            Administration = null,
        },
          // Collective Labor Agreements for Provider 3
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 30,
            ParentInheritanceLevelId = 0,
            ProviderId = 0,
            Description = "CAO_03",
            Comment = "Collective Labor Agreement for Provider2",
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 3,
                Type = (int)InheritanceLevel.CollectiveLaborAgreement
            },
            ParentInheritanceLevel = null,
            Provider = null,
            Administration = null,
        },
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 4,
            ParentInheritanceLevelId = 0,
            ProviderId = 0,
            Description = "CAO_04",
            Comment = "Collective Labor Agreement for Provider2",
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 4,
                Type = (int)InheritanceLevel.CollectiveLaborAgreement
            },
            ParentInheritanceLevel = null,
            Provider = null,
            Administration = null,
        },
    ];
    #endregion

    #region WageModels
    // To make the testdata more intuitive we use InheritanceLevelId's in the 100 to 999 range
    public static Repository.Entities.Base.InheritanceLevel[] WageModels =
    [
        // WageModels for Provider 1
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 100,
            ParentInheritanceLevelId = CollectiveLaborAgreements[0].InheritanceLevelId,
            ProviderId = Providers[0].ProviderId,
            Description = "WageModel01",
            Comment = "Some comment for wageModel01",
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 100,
                Type = (int)InheritanceLevel.WageModel
            },
            ParentInheritanceLevel = CollectiveLaborAgreements[0],
            Provider = Providers[0],
            Administration = null,
        },
        // WageModels for Provider 2
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 200,
            ParentInheritanceLevelId = CollectiveLaborAgreements[2].InheritanceLevelId,
            ProviderId = Providers[1].ProviderId,
            Description = "A first WageModel for CLA from Provider2",
            Comment = null,
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 200,
                Type = (int)InheritanceLevel.WageModel
            },
            ParentInheritanceLevel = CollectiveLaborAgreements[2],
            Provider = Providers[1],
            Administration = null,
        },
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = 201,
            ParentInheritanceLevelId = CollectiveLaborAgreements[2].InheritanceLevelId,
            ProviderId = Providers[1].ProviderId,
            Description = "A second WageModel for CLA from Provider2",
            Comment = null,
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 201,
                Type = (int)InheritanceLevel.WageModel
            },
            ParentInheritanceLevel = CollectiveLaborAgreements[2],
            Provider = Providers[1],
            Administration = null,
        }
    ];
    #endregion

    #region Employers
    public static Employer[] Employers = Enumerable.Range(0, 3).Select(value => new Employer
    {
        Id = Guid.NewGuid(),
        EmployerId = value + 1,
        Name = $"Employer {value + 1}",
        TeamEmployers =
        [
            new TeamEmployer
            {
                Team = new Team
                {
                    TeamId = value + 1,
                    TeamUsers =
                    [
                        new TeamUser
                        {
                            User = Users[0]
                        }
                    ]
                }
            }
        ]

    }).ToArray();
    #endregion

    #region Administrations
    public static Administration[] Administrations = Enumerable.Range(0, 3).Select(value => new Administration
    {
        Id = Guid.NewGuid(),
        Name = $"Administration {value + 1}",
        ClientNumber = value + 1,
        GroupCode = value + 1,
        AdministrationNumber = $"{value + 1}",
        Employer = Employers[value]
    }).ToArray();
    #endregion

    #region PayrollAdministrations
    public static Repository.Entities.Base.InheritanceLevel[] PayrollAdministrations =
    [
        // PayrollAdministration for Provider 1
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = Administrations[0].InheritanceLevelId,
            Administration = Administrations[0],

            ParentInheritanceLevelId = WageModels[0].InheritanceLevelId,

            ProviderId = Providers[0].ProviderId,
            Provider = Providers[0],

            Description = "PayrollAdministration01",
            Comment = "Some comment for PayrollAdministration01",
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 300,
                Type = (int)InheritanceLevel.PayrollAdministration
            },
        },
        // PayrollAdministration for Provider 2
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = Administrations[1].InheritanceLevelId,
            Administration = Administrations[1],

            ParentInheritanceLevelId = WageModels[1].InheritanceLevelId,

            ProviderId = Providers[1].ProviderId,
            Provider = Providers[1],

            Description = "A first PayrollAdministration for CLA from Provider2",
            Comment = null,
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 301,
                Type = (int)InheritanceLevel.PayrollAdministration
            },
        },
        new Repository.Entities.Base.InheritanceLevel
        {
            Id = Guid.NewGuid(),
            InheritanceLevelId = Administrations[2].InheritanceLevelId,
            Administration = Administrations[2],

            ParentInheritanceLevelId = WageModels[1].InheritanceLevelId,

            Provider = Providers[1],
            ProviderId = Providers[1].ProviderId,

            Description = "A second PayrollAdministration for CLA from Provider2",
            Comment = null,
            InheritanceLevelInfo = new InheritanceLevelInfo
            {
                InheritanceLevelId = 302,
                Type = (int)InheritanceLevel.PayrollAdministration
            },
        }
    ];
    #endregion

    #region PayrollPeriodTypes
    public static CtPayrollPeriodType[] CtPayrollPeriodTypes =
    [
        // PayrollAdministration for Provider 1
        new CtPayrollPeriodType
        {
            Code = 1,
            Omschrijving = "Maand",
        },
    ];
    #endregion
}
