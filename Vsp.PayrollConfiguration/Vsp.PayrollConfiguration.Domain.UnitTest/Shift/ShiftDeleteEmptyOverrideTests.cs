using System.Reflection;
using System.Text;
using Vsp.PayrollConfiguration.Domain.UnitTest.Shared;

namespace Vsp.PayrollConfiguration.Domain.UnitTest.Shift;

[TestClass]
public class ShiftDeleteEmptyOverrideTests
{
    private static IConfiguration configuration;
    private LoketContext loketContext;

    private InheritanceLevel collectiveLaborAgreement;
    private InheritanceLevel wageModel;
    private InheritanceLevel payrollAdministration;

    [ClassInitialize]
    public static void ClassInitialize(TestContext _) => configuration = new ConfigurationBuilder().AddJsonFile("appsettings.Test.json").Build();

    [TestInitialize]
    public void TestInitialize()
    {
        this.loketContext = MockContextCreator.Create<LoketContext>(sqlConnectionString: configuration.GetConnectionString("VspQA"), null);

        this.payrollAdministration = this.loketContext.InheritanceLevels
            .Include(x => x.ParentInheritanceLevel).ThenInclude(x => x.ParentInheritanceLevel)
            .Where(x => x.Id == SharedConstants.PayrollAdministrations.QA_PayrollConfiguration1_PA_ForLevel_PA)
            .Single();

        this.wageModel = this.payrollAdministration.ParentInheritanceLevel;
        this.collectiveLaborAgreement = this.wageModel.ParentInheritanceLevel;
    }

    [TestCleanup]
    public void TestCleanup() => this.loketContext.Dispose();

    [TestMethod]
    [DynamicData(nameof(Permutations), DynamicDataDisplayName = nameof(GetDynamicDataDisplayName))]
    public async Task ShiftDeleteEmptyOverrideTest(ShiftDeleteEmptyOverrideTestCase testCase)
    {
        // Arrange
        var yearId = 2025;
        var shiftId = 1;
        var payrollPeriodId = 1;

        if (testCase.CLA != null)
        {
            var shift = new ModelShift
            {
                InheritanceLevelId = this.collectiveLaborAgreement.InheritanceLevelId,
                YearId = yearId,
                ShiftId = shiftId,
                PayrollPeriodId = payrollPeriodId,

                FullTimeHoursPerWeek = testCase.CLA.FullTimeHoursPerWeek,
                BonusPercentage = testCase.CLA.BonusPercentage,
            };
            this.loketContext.Add(shift);
        }
        if (testCase.WM != null)
        {
            var shift = new ModelShift
            {
                InheritanceLevelId = this.wageModel.InheritanceLevelId,
                YearId = yearId,
                ShiftId = shiftId,
                PayrollPeriodId = payrollPeriodId,

                FullTimeHoursPerWeek = testCase.WM.FullTimeHoursPerWeek,
                BonusPercentage = testCase.WM.BonusPercentage,
            };
            this.loketContext.Add(shift);
        }
        if (testCase.PA != null)
        {
            var shift = new ModelShift
            {
                InheritanceLevelId = this.payrollAdministration.InheritanceLevelId,
                YearId = yearId,
                ShiftId = shiftId,
                PayrollPeriodId = payrollPeriodId,

                FullTimeHoursPerWeek = testCase.PA.FullTimeHoursPerWeek,
                BonusPercentage = testCase.PA.BonusPercentage,
            };
            this.loketContext.Add(shift);
        }
        await this.loketContext.SaveChangesAsync();

        var preWageModel = await this.loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.wageModel.InheritanceLevelId && x.YearId == yearId && x.ShiftId == shiftId && x.PayrollPeriodId == payrollPeriodId)
            .SingleAsync();

        var prePayrollAdministration = await this.loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.payrollAdministration.InheritanceLevelId && x.YearId == yearId && x.ShiftId == shiftId && x.PayrollPeriodId == payrollPeriodId)
            .SingleAsync();

        // Act
        switch (testCase.Delete)
        {
            case Delete.WM:
                await this.loketContext.Set<ModelShift>()
                    .Where(x => x.InheritanceLevelId == this.wageModel.InheritanceLevelId && x.YearId == yearId && x.ShiftId == shiftId && x.PayrollPeriodId == payrollPeriodId)
                    .ExecuteDeleteAsync();
                break;

            case Delete.PA:
                await this.loketContext.Set<ModelShift>()
                    .Where(x => x.InheritanceLevelId == this.payrollAdministration.InheritanceLevelId && x.YearId == yearId && x.ShiftId == shiftId && x.PayrollPeriodId == payrollPeriodId)
                    .ExecuteDeleteAsync();
                break;
        }

        // Assert
        var postWageModel = await this.loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.wageModel.InheritanceLevelId && x.YearId == yearId && x.ShiftId == shiftId && x.PayrollPeriodId == payrollPeriodId)
            .SingleAsync();

        var postPayrollAdministration = await this.loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.payrollAdministration.InheritanceLevelId && x.YearId == yearId && x.ShiftId == shiftId && x.PayrollPeriodId == payrollPeriodId)
            .SingleAsync();

        postWageModel.Should().BeEquivalentTo(preWageModel, opt => opt.Excluding(x => x.ShiftIdDefinedAtLevel));
        postPayrollAdministration.Should().BeEquivalentTo(prePayrollAdministration, opt => opt.Excluding(x => x.ShiftIdDefinedAtLevel));

        switch (testCase.Delete)
        {
            case Delete.WM:
                postWageModel.ShiftIdDefinedAtLevel.Should().Be(1);
                postPayrollAdministration.ShiftIdDefinedAtLevel.Should().Be(testCase.PA != null ? 3 : 1);
                break;

            case Delete.PA:
                postWageModel.ShiftIdDefinedAtLevel.Should().Be(testCase.WM != null ? 2 : 1);
                postPayrollAdministration.ShiftIdDefinedAtLevel.Should().Be(testCase.WM != null ? 2 : 1);
                break;
        }
    }

    public static string GetDynamicDataDisplayName(MethodInfo _, object[] data) => data[0].ToString();

    public static IEnumerable<object[]> Permutations
    {
        get
        {
            var result = new List<object[]>();

            var deletes = Enum.GetValues<Delete>();

            var clas = new ShiftValues[]
            {
                null,
                new(11, 12),
            };

            var wms = new ShiftValues[]
            {
                null,
                ShiftValues.Empty,
                new(null, 22),
                new(21, null),
                new(21, 22),
            };

            var pas = new ShiftValues[]
            {
                null,
                ShiftValues.Empty,
                new(null, 32),
                new(31, null),
                new(31, 32),
            };

            for (var d = 0; d < deletes.Length; d++)
            {
                for (var c = 0; c < clas.Length; c++)
                {
                    var cla = clas[c];
                    for (var w = 0; w < wms.Length; w++)
                    {
                        var wm = wms[w];
                        for (var p = 0; p < pas.Length; p++)
                        {
                            var pa = pas[p];

                            var delete = deletes[d];

                            // Level to delete needs to be empty line: (null, null)
                            if (delete == Delete.WM && wm != ShiftValues.Empty) continue;
                            if (delete == Delete.PA && pa != ShiftValues.Empty) continue;

                            // If deleting WM, CLA needs to exist
                            if (delete == Delete.WM && cla == null) continue;

                            // If deleting PA, WM or CLA need to exist
                            if (delete == Delete.PA && cla == null && wm == null) continue;

                            // Always need a non-null value for each property at PA level
                            if (cla?.FullTimeHoursPerWeek == null && wm?.FullTimeHoursPerWeek == null && pa?.FullTimeHoursPerWeek == null) continue;
                            if (cla?.BonusPercentage == null && wm?.BonusPercentage == null && pa?.BonusPercentage == null) continue;

                            var testCase = new ShiftDeleteEmptyOverrideTestCase()
                            {
                                Delete = delete,
                                CLA = cla,
                                WM = wm,
                                PA = pa,
                            };
                            result.Add([testCase]);
                        }
                    }
                }
            }

            return result;

        }
    }
}

public class ShiftDeleteEmptyOverrideTestCase
{
    public required Delete Delete { get; init; }
    public required ShiftValues CLA { get; init; }
    public required ShiftValues WM { get; init; }
    public required ShiftValues PA { get; init; }

    public override string ToString()
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.Append($"Delete = {this.Delete}");
        stringBuilder.Append($", CLA = {this.CLA?.ToString() ?? "null"}");
        stringBuilder.Append($", WM = {this.WM?.ToString() ?? "null"}");
        stringBuilder.Append($", PA = {this.PA?.ToString() ?? "null"}");

        return stringBuilder.ToString();
    }
}

public record ShiftValues(decimal? FullTimeHoursPerWeek, decimal? BonusPercentage)
{
    public static readonly ShiftValues Empty = new(null, null);

    public override string ToString() => $"({this.FullTimeHoursPerWeek?.ToString() ?? "null"}, {this.BonusPercentage?.ToString() ?? "null"})";
}

public enum Delete { WM, PA }
