using Vsp.Commands.HttpClients;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Mappers;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Mappers;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Queries;
using Vsp.PayrollConfiguration.Domain.WageModel.Mappers;

namespace Vsp.PayrollConfiguration.Domain.UnitTest.PayrollAdministration.Queries;

[TestClass]
public class GetPayrollAdministrationsQueryTests
{
    private LoketContext loketContext;
    private ICurrentContext currentContext;
    private GetPayrollAdministrationsQuery query;
    private IGlobalFilterClient globalFilterClient;
    private static IMapper mapper;

    [ClassInitialize]
    public static void ClassInitialize(TestContext _)
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PayrollAdministrationProfile>();
            cfg.AddProfile<WageModelProfile>();
            cfg.AddProfile<CollectiveLaborAgreementProfile>();
        });
        config.AssertConfigurationIsValid();
        mapper = new Mapper(config);
    }

    [TestInitialize]
    public void TestInitialize()
    {
        this.loketContext = MockContextCreator.Create<LoketContext>();
        var logger = Substitute.For<ILogger<IBaseQuery>>();
        this.currentContext = Substitute.For<ICurrentContext>();
        this.currentContext.Rol.Returns(RolEnum.Provider);

        var entityFilterFactory = Substitute.For<IEntityFilterFactory>();
        entityFilterFactory.GetEntityFilter<PayrollAdministrationModel>().Returns((IEntityFilter<PayrollAdministrationModel>)null);

        this.globalFilterClient = Substitute.For<IGlobalFilterClient>();
        this.currentContext.SkipGlobalFilter.Returns(true);

        var dependencies = new FilteredQueryWithGlobalFilterDependencies<ILoketContext>(this.loketContext, logger, mapper, this.currentContext, entityFilterFactory, this.globalFilterClient);

        this.query = new GetPayrollAdministrationsQuery(dependencies);
    }

    [TestCleanup]
    public void TestCleanup() => this.loketContext.Dispose();

    [TestMethod]
    public async Task GivenSkipGlobalFilterTrue_WhenExecuteList_ThenGlobalFilterClientIsNotCalled()
    {
        // Arrange
        var userId = Guid.NewGuid();
        this.currentContext.SkipGlobalFilter.Returns(true);

        // Act
        var result = await this.query.ExecuteList(userId);

        // Assert
        result.Should().NotBeNull();
        // Verify that the global filter client was NOT called
        await this.globalFilterClient.DidNotReceive().GetFilteredPayrollAdministrationIdsForCurrentUser();
    }

    [TestMethod]
    public async Task GivenSkipGlobalFilterFalse_WhenExecuteList_ThenGlobalFilterClientIsCalled()
    {
        // Arrange
        var userId = Guid.NewGuid();
        this.currentContext.SkipGlobalFilter.Returns(false);
        this.globalFilterClient.GetFilteredPayrollAdministrationIdsForCurrentUser()
            .Returns(new GlobalFilterResultModel { MustFilter = true, Ids = new List<Guid>() });

        // Act
        var result = await this.query.ExecuteList(userId);

        // Assert
        result.Should().NotBeNull();
        // Verify that the global filter client WAS called exactly once
        await this.globalFilterClient.Received(1).GetFilteredPayrollAdministrationIdsForCurrentUser();
    }

    [TestMethod]
    public async Task GivenCurrentUserWithGlobalFilter_WhenExecuteList_ThenReturnFilteredPayrollAdministrations()
    {
        // Arrange
        // Using a user id that in our test data returns multiple payroll administrations.
        var userId = new Guid("56C000AF-A2DA-4A58-B9EA-0028AA59372B");
        // Suppose that, without filtering, this user has 3 payroll administrations,
        // but the global filter should limit the result to just the one with this id.
        var allowedPayrollAdministrationId = new Guid("1CFDFCC0-DA95-4804-8BB6-B13D4A115EDE");

        this.currentContext.SkipGlobalFilter.Returns(false);
        this.globalFilterClient.GetFilteredPayrollAdministrationIdsForCurrentUser()
            .Returns(new GlobalFilterResultModel { MustFilter = true, Ids = [allowedPayrollAdministrationId] });

        // Act
        var result = await this.query.ExecuteList(userId);

        // Assert
        result.Should().NotBeNull();
        result.ResultObject.Should().NotBeNull();
        // Only one payroll administration should pass the filter
        result.ResultObject.Should().HaveCount(1);
        result.ResultObject.Single().Id.Should().Be(allowedPayrollAdministrationId);
        await this.globalFilterClient.Received(1).GetFilteredPayrollAdministrationIdsForCurrentUser();
    }
}
