global using System.Linq.Expressions;
global using System.Reflection;
global using AutoMapper;
global using FluentValidation;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using Newtonsoft.Json;
global using Vsp.ApiBase.Authorization;
global using Vsp.ApiBase.Interfaces;
global using Vsp.AuthorizationService.Internal.ApiProtocol.Authorization;
global using Vsp.Commands;
global using Vsp.Entities.EntityFramework;
global using Vsp.Entities.GeneratedId;
global using Vsp.Infrastructure;
global using Vsp.Infrastructure.Auditing;
global using Vsp.PayrollConfiguration.Repository.Entities.Base;
global using Vsp.Validation;
