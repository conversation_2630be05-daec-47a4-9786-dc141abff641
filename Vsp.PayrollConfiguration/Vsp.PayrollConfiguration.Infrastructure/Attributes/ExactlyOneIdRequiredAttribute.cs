using System.ComponentModel.DataAnnotations;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.Infrastructure.Attributes;

/// <summary>
/// Validation of <see cref="InheritanceLevelQuerystringParams"/>, specifically that exactly one of the following querystring parameters is required and set: collectiveLaborAgreementId, wageModelId, or payrollAdministrationId.
/// </summary>
[AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false)]
public class ExactlyOneIdRequiredAttribute : ValidationAttribute
{
    private const string DefaultErrorMessage = "Exactly one of the following querystring parameters is required and must be set: collectiveLaborAgreementId, wageModelId, or payrollAdministrationId.";
    private const string TypeErrorMessage = "The validation attribute is not applied on the correct type.";

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        var obj = validationContext.ObjectInstance;
        if (obj.GetType() != typeof(InheritanceLevelQuerystringParams))
        {
            return new ValidationResult(TypeErrorMessage);
        }

        var model = (InheritanceLevelQuerystringParams)validationContext.ObjectInstance;
        var count = 0;
        if (model.CollectiveLaborAgreementId.HasValue) count++;
        if (model.WageModelId.HasValue) count++;
        if (model.PayrollAdministrationId.HasValue) count++;

        return count == 1 ? ValidationResult.Success : new ValidationResult(this.ErrorMessage ?? DefaultErrorMessage, ["queryStringParams"]);
    }
}
