using Microsoft.AspNetCore.Mvc;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Infrastructure.Models;

/// <summary>
/// Model to capture the different query strings for each <see cref="InheritanceLevel"/>.
/// </summary>
public class InheritanceLevelQuerystringParams
{
    /// <summary>
    /// The unique identifier of a collective labor agreement (GUID/UUID).
    /// </summary>
    /// <example>123e4567-e89b-12d3-a456-************</example>
    [BindProperty(Name = "collectiveLaborAgreementId", SupportsGet = true)]
    public Guid? CollectiveLaborAgreementId { get; init; }

    /// <summary>
    /// The unique identifier of a wage model (GUID/UUID).
    /// </summary>
    /// <example>123e4567-e89b-12d3-a456-************</example>
    [BindProperty(Name = "wageModelId", SupportsGet = true)]
    public Guid? WageModelId { get; init; }

    /// <summary>
    /// The unique identifier of a payroll administration (GUID/UUID).
    /// </summary>
    /// <example>123e4567-e89b-12d3-a456-************</example>
    [BindProperty(Name = "payrollAdministrationId", SupportsGet = true)]
    public Guid? PayrollAdministrationId { get; init; }

    public Guid GetId()
    {
        if (!IsExactlyOneIdSet())
        {
            throw new InvalidOperationException($"Not exactly one ID set! {this}");
        }

        return this.CollectiveLaborAgreementId ?? this.WageModelId ?? this.PayrollAdministrationId!.Value;
    }

    public InheritanceLevel GetInheritanceLevel()
    {
        if (!IsExactlyOneIdSet())
        {
            throw new InvalidOperationException($"Not exactly one ID set! {this}");
        }

        return this.CollectiveLaborAgreementId.HasValue
            ? InheritanceLevel.CollectiveLaborAgreement
            : this.WageModelId.HasValue ? InheritanceLevel.WageModel : InheritanceLevel.PayrollAdministration;
    }

    private bool IsExactlyOneIdSet()
    {
        var count = 0;
        if (this.CollectiveLaborAgreementId.HasValue) count++;
        if (this.WageModelId.HasValue) count++;
        if (this.PayrollAdministrationId.HasValue) count++;
        return count == 1;
    }

    public override string ToString() =>
        $"collectiveLaborAgreementId: {this.CollectiveLaborAgreementId?.ToString() ?? "<empty>"}, wageModelId: {this.WageModelId?.ToString() ?? "<empty>"}, payrollAdministrationId: {this.PayrollAdministrationId?.ToString() ?? "<empty>"}";
}
