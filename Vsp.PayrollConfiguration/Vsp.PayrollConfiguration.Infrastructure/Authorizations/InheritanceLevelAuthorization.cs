using Vsp.PayrollConfiguration.Infrastructure.Models;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Infrastructure.Authorizations;

public class InheritanceLevelAuthorizationModel
{
    public InheritanceLevelQuerystringParams QuerystringParams { get; set; } = null!;
}

/// <summary>
/// Variant of <see cref="AuthorizeLoketBase{T}"/> that authorizes on each <see cref="InheritanceLevel"/> available in <see cref="InheritanceLevelQuerystringParams"/>.
/// <para><see cref="InheritanceLevel.CollectiveLaborAgreement"/> -> <see cref="ResourceType.CollectieveArbeidsOvereenkomst"/></para>
/// <para><see cref="InheritanceLevel.WageModel"/> -> <see cref="ResourceType.LoonModel"/></para>
/// <para><see cref="InheritanceLevel.PayrollAdministration"/> -> <see cref="ResourceType.Administratie"/></para>
/// </summary>
public class InheritanceLevelAuthorization : AuthorizeLoketBase<InheritanceLevelAuthorizationModel>
{
    public override Task<(ResourceType ResourceType, Guid EntityId)> AuthorizeLoketEntity(ICurrentContext currentContext, InheritanceLevelAuthorizationModel authorizationObject)
    {
        var id = authorizationObject.QuerystringParams.GetId();

        // NOTE: The authorization service checks whether the given ID is *really* from the given level, we don't duplicate that check here
        InheritanceLevel? inheritanceLevel = authorizationObject.QuerystringParams.GetInheritanceLevel();

        var resourceType = inheritanceLevel switch
        {
            InheritanceLevel.CollectiveLaborAgreement => ResourceType.CollectieveArbeidsOvereenkomst,
            InheritanceLevel.WageModel => ResourceType.LoonModel,
            InheritanceLevel.PayrollAdministration => ResourceType.Administratie,
            _ => throw new NotImplementedException($"Unhandled inheritance level: {inheritanceLevel}")
        };

        return Task.FromResult((resourceType, id));
    }

    public override Task<InheritanceLevel?> AuthorizeLoketInheritanceLevel(ICurrentContext currentContext, InheritanceLevelAuthorizationModel authorizationObject)
    {
        InheritanceLevel? inheritanceLevel = authorizationObject.QuerystringParams.GetInheritanceLevel();
        return Task.FromResult(inheritanceLevel);
    }
}
