using Vsp.PayrollConfiguration.CollectiveLaborAgreement.Constants;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Authorizations;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Interfaces;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;

namespace Vsp.PayrollConfiguration.CollectiveLaborAgreement.Controllers;

/// <summary>
/// Collective labor agreement (CLA) related endpoints (NL: collectieve arbeidsovereenkomst)
/// </summary>
[Tags("Collective Labor Agreement (CLA)")]
[Authorize]
[ApiController]
public class CollectiveLaborAgreementController(IResultHandler resultHandler, ICollectiveLaborAgreementService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly ICollectiveLaborAgreementService service = service;

    /// <summary>
    /// List of collective labor agreements
    /// </summary>
    /// <remarks>
    /// **Activity name**: <c>GetCollectiveLaborAgreementsByBearerToken</c><br/>
    /// Retrieves a list of collective labor agreements for the current logged-in user.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(CollectiveLaborAgreementRoutes.GetCollectiveLaborAgreementsByBearerTokenAsync)]
    [AuthorizeEntity<UserAuthorizationModel>("352f91a4-2381-440d-987c-956a605852cb")] // GetCollectiveLaborAgreementsByBearerToken
    public async Task<ActionResult<ListResult<CollectiveLaborAgreementModel>>> GetCollectiveLaborAgreementsByBearerTokenAsync()
    {
        var result = await this.service.GetCollectiveLaborAgreementsByBearerTokenAsync();
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Details of a collective labor agreement
    /// </summary>
    /// <remarks>
    /// **Activity name**: <c>GetCollectiveLaborAgreementByCollectiveLaborAgreementId</c><br/>
    /// Retrieves details of a collective labor agreement.
    /// </remarks>
    /// <param name="collectiveLaborAgreementId" example="123e4567-e89b-12d3-a456-************">The unique identifier of a collective labor agreement (GUID/UUID).</param>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(CollectiveLaborAgreementRoutes.GetCollectiveLaborAgreementByCollectiveLaborAgreementIdAsync)]
    [AuthorizeEntity<CollectiveLaborAgreementAuthorizationModel>("d91b9b7a-b735-47b8-b8b8-84e9e7312755")] // GetCollectiveLaborAgreementByCollectiveLaborAgreementId
    public async Task<ActionResult<DetailResult<CollectiveLaborAgreementModel>>> GetCollectiveLaborAgreementByCollectiveLaborAgreementIdAsync(Guid collectiveLaborAgreementId)
    {
        var result = await this.service.GetCollectiveLaborAgreementByCollectiveLaborAgreementIdAsync(collectiveLaborAgreementId);
        return this.resultHandler.ToTypedActionResult(result);
    }
}
