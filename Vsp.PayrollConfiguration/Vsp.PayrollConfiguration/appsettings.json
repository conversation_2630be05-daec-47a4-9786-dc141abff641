{"AllowedHosts": "*", "ApplicationInsights": {"ConnectionString": "#{ApplicationInsights.ConnectionString}#"}, "LoggingConfiguration": {"ApplicationName": "Vsp.PayrollConfiguration"}, "Logging": {"LogLevel": {"Default": "Warning"}, "ApplicationInsights": {"Default": "Warning"}}, "ConnectionStrings": {"SettingsDatabase": "#{ConnectionStrings.SettingsDatabase}#", "Vsp.GlobalFilter.Core.InternalService.BaseUri": "#{GlobalFilterServiceUrl}#"}, "ApiBase": {"AuthenticationServiceUrl": "#{AuthenticationServiceExternalUrl}#", "AuthorizationServiceUrl": "#{AuthorizationServiceUrl}#", "ServiceBus": "#{ConnectionStrings.ServiceBus}#", "RedisCache": "#{ConnectionStrings.RedisCache}#", "AICoreServiceUrl": "#{AICoreServiceUrl}#", "KubernetesSubnet": "#{K8S.Subnet}#", "KeyVault": {"Url": "#{KeyVault.Url}#", "K8SClientId": "#{KeyVault.K8SClientId}#"}}, "MachineKey": {"DecryptionKey": "#{Machine<PERSON>ey.DecryptionKey}#", "ValidationKey": "#{MachineKey.ValidationKey}#"}}