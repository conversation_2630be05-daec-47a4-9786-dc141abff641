{"_embedded": [{"accrualEndPayrollPeriod": null, "advancePayrollComponent": null, "advancePayrollPeriod": null, "advancePercentage": 0.0, "baseType": null, "calculationPayrollPeriod": null, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "baseType": {"key": 3, "value": "PayrollAdministration"}, "calculationPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}, "financialMarkupPercentage": {"key": 3, "value": "PayrollAdministration"}, "financialReservationPercentage": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isAutomaticCalculation": {"key": 3, "value": "PayrollAdministration"}, "isCumulativeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPartTimeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtEndOfEmployment": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtStartOfEmployment": {"key": 3, "value": "PayrollAdministration"}, "isSupplementingDailyWage": {"key": 3, "value": "PayrollAdministration"}, "minimumMaximumType": {"key": 3, "value": "PayrollAdministration"}, "payoutPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "payslipType": {"key": 3, "value": "PayrollAdministration"}, "percentage": {"key": 3, "value": "PayrollAdministration"}, "periodicReservationPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "referencePayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "reservationPercentage": {"key": 3, "value": "PayrollAdministration"}, "resultPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}}, "description": "BFC_PA_4_1", "endEmployeeAge": 0.0, "endEmployeeAgeType": null, "financialMarkupPercentage": 0.0, "financialReservationPercentage": 0.0, "id": "000009a8-07e9-0004-0100-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": true, "isCumulativeCalculation": false, "isPartTimeCalculation": false, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": false, "key": 4, "minimumMaximumType": {"key": 1, "value": "Grondslag"}, "payoutPayrollPeriod": null, "payslipType": {"key": 4, "value": "Normale strook"}, "percentage": 0.0, "periodicReservationPayrollComponent": null, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "VERLOONDE UREN", "key": 498}, "startEmployeeAge": 0.0, "startEmployeeAgeType": null, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, {"accrualEndPayrollPeriod": {"periodNumber": 4}, "advancePayrollComponent": {"category": {"key": 10, "value": "Netto/bruto"}, "description": "NETTO LOON", "key": 121}, "advancePayrollPeriod": {"periodNumber": 1}, "advancePercentage": 3.000331, "baseType": {"key": 1, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "calculationPayrollPeriod": {"periodNumber": 1}, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "baseType": {"key": 3, "value": "PayrollAdministration"}, "calculationPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}, "financialMarkupPercentage": {"key": 3, "value": "PayrollAdministration"}, "financialReservationPercentage": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isAutomaticCalculation": {"key": 3, "value": "PayrollAdministration"}, "isCumulativeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPartTimeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtEndOfEmployment": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtStartOfEmployment": {"key": 3, "value": "PayrollAdministration"}, "isSupplementingDailyWage": {"key": 3, "value": "PayrollAdministration"}, "minimumMaximumType": {"key": 3, "value": "PayrollAdministration"}, "payoutPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "payslipType": {"key": 3, "value": "PayrollAdministration"}, "percentage": {"key": 3, "value": "PayrollAdministration"}, "periodicReservationPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "referencePayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "reservationPercentage": {"key": 3, "value": "PayrollAdministration"}, "resultPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}}, "description": "BFC_PA_3_1", "endEmployeeAge": 0.75, "endEmployeeAgeType": {"key": 5, "value": "Eerste van de periode NA bereiken AOW-leeftijd"}, "financialMarkupPercentage": 3.000331, "financialReservationPercentage": 3.000331, "id": "000009a8-07e9-0003-0100-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": true, "isPartTimeCalculation": false, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": false, "key": 3, "minimumMaximumType": {"key": 2, "value": "Resultaat"}, "payoutPayrollPeriod": {"periodNumber": 3}, "payslipType": {"key": 5, "value": "Bijzonder tarief strook"}, "percentage": 3.000331, "periodicReservationPayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "UREN ONB.VERZ.", "key": 4}, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "UREN VAKANTIE", "key": 2}, "startEmployeeAge": 0.25, "startEmployeeAgeType": {"key": 4, "value": "Eerste van de periode VAN bereiken AOW-leeftijd"}, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, {"accrualEndPayrollPeriod": null, "advancePayrollComponent": {"category": {"key": 7, "value": "<PERSON><PERSON><PERSON> vergo<PERSON> tabel"}, "description": "REISKST.BELAST", "key": 283}, "advancePayrollPeriod": null, "advancePercentage": 3.000003, "baseType": null, "calculationPayrollPeriod": null, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 2, "value": "WageModel"}, "baseType": {"key": 2, "value": "WageModel"}, "calculationPayrollPeriod": {"key": 2, "value": "WageModel"}, "description": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}, "financialMarkupPercentage": {"key": 2, "value": "WageModel"}, "financialReservationPercentage": {"key": 2, "value": "WageModel"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isAutomaticCalculation": {"key": 2, "value": "WageModel"}, "isCumulativeCalculation": {"key": 2, "value": "WageModel"}, "isPartTimeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtEndOfEmployment": {"key": 2, "value": "WageModel"}, "isPayoutAtStartOfEmployment": {"key": 2, "value": "WageModel"}, "isSupplementingDailyWage": {"key": 3, "value": "PayrollAdministration"}, "minimumMaximumType": {"key": 3, "value": "PayrollAdministration"}, "payoutPayrollPeriod": {"key": 2, "value": "WageModel"}, "payslipType": {"key": 3, "value": "PayrollAdministration"}, "percentage": {"key": 3, "value": "PayrollAdministration"}, "periodicReservationPayrollComponent": {"key": 2, "value": "WageModel"}, "referencePayrollPeriod": {"key": 2, "value": "WageModel"}, "reservationPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollPeriod": {"key": 2, "value": "WageModel"}, "reservationPercentage": {"key": 3, "value": "PayrollAdministration"}, "resultPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}}, "description": "BFC_WM_2_2_OVERRIDES@PA", "endEmployeeAge": 0.5, "endEmployeeAgeType": {"key": 8, "value": "Eerste van de maand NA bereiken leeftijd"}, "financialMarkupPercentage": 0.0, "financialReservationPercentage": 0.0, "id": "000009a8-07e9-0002-0200-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": false, "isPartTimeCalculation": true, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": true, "key": 2, "minimumMaximumType": {"key": 1, "value": "Grondslag"}, "payoutPayrollPeriod": null, "payslipType": {"key": 5, "value": "Bijzonder tarief strook"}, "percentage": 3.000333, "periodicReservationPayrollComponent": null, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "UREN OPN OSV", "key": 4121}, "startEmployeeAge": 0.25, "startEmployeeAgeType": {"key": 7, "value": "Eerste van de maand VAN bereiken leeftijd"}, "startPayrollPeriod": {"payrollPeriodId": 202502, "periodEndDate": "2025-02-28", "periodNumber": 2, "periodStartDate": "2025-02-01", "year": 2025}, "year": 2025}, {"accrualEndPayrollPeriod": null, "advancePayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "UREN SPEC.BEZ.", "key": 22}, "advancePayrollPeriod": null, "advancePercentage": 2.222001, "baseType": {"key": 2, "value": "Arbeidsvw.bedrag (vh extra periodesalaris)"}, "calculationPayrollPeriod": null, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 2, "value": "WageModel"}, "baseType": {"key": 2, "value": "WageModel"}, "calculationPayrollPeriod": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "endEmployeeAge": {"key": 2, "value": "WageModel"}, "endEmployeeAgeType": {"key": 2, "value": "WageModel"}, "financialMarkupPercentage": {"key": 2, "value": "WageModel"}, "financialReservationPercentage": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isAutomaticCalculation": {"key": 2, "value": "WageModel"}, "isCumulativeCalculation": {"key": 2, "value": "WageModel"}, "isPartTimeCalculation": {"key": 2, "value": "WageModel"}, "isPayoutAtEndOfEmployment": {"key": 2, "value": "WageModel"}, "isPayoutAtStartOfEmployment": {"key": 2, "value": "WageModel"}, "isSupplementingDailyWage": {"key": 2, "value": "WageModel"}, "minimumMaximumType": {"key": 2, "value": "WageModel"}, "payoutPayrollPeriod": {"key": 2, "value": "WageModel"}, "payslipType": {"key": 2, "value": "WageModel"}, "percentage": {"key": 2, "value": "WageModel"}, "periodicReservationPayrollComponent": {"key": 2, "value": "WageModel"}, "referencePayrollPeriod": {"key": 2, "value": "WageModel"}, "reservationPayrollComponent": {"key": 2, "value": "WageModel"}, "reservationPayrollPeriod": {"key": 2, "value": "WageModel"}, "reservationPercentage": {"key": 2, "value": "WageModel"}, "resultPayrollComponent": {"key": 2, "value": "WageModel"}, "startEmployeeAge": {"key": 2, "value": "WageModel"}, "startEmployeeAgeType": {"key": 2, "value": "WageModel"}}, "description": "BFC_WM_2_1", "endEmployeeAge": 0.75, "endEmployeeAgeType": {"key": 3, "value": "Op het bereiken van leeftijd"}, "financialMarkupPercentage": 2.000001, "financialReservationPercentage": 2.222001, "id": "000009a8-07e9-0002-0100-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": false, "isPartTimeCalculation": false, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": true, "key": 2, "minimumMaximumType": {"key": 1, "value": "Grondslag"}, "payoutPayrollPeriod": null, "payslipType": {"key": 5, "value": "Bijzonder tarief strook"}, "percentage": 2.000221, "periodicReservationPayrollComponent": null, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 10, "value": "Netto/bruto"}, "description": "NETTO TOESL.1", "key": 128}, "startEmployeeAge": 0.5, "startEmployeeAgeType": {"key": 3, "value": "Op het bereiken van leeftijd"}, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, {"accrualEndPayrollPeriod": {"periodNumber": 4}, "advancePayrollComponent": {"category": {"key": 8, "value": "<PERSON><PERSON><PERSON> vergo<PERSON> ta<PERSON>"}, "description": "PROVISIE", "key": 315}, "advancePayrollPeriod": {"periodNumber": 4}, "advancePercentage": 0.110002, "baseType": {"key": 1, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "calculationPayrollPeriod": {"periodNumber": 1}, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseType": {"key": 1, "value": "CollectiveLaborAgreement"}, "calculationPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "endEmployeeAge": {"key": 1, "value": "CollectiveLaborAgreement"}, "endEmployeeAgeType": {"key": 1, "value": "CollectiveLaborAgreement"}, "financialMarkupPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "financialReservationPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isAutomaticCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isCumulativeCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPartTimeCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayoutAtEndOfEmployment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayoutAtStartOfEmployment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isSupplementingDailyWage": {"key": 1, "value": "CollectiveLaborAgreement"}, "minimumMaximumType": {"key": 1, "value": "CollectiveLaborAgreement"}, "payoutPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "payslipType": {"key": 1, "value": "CollectiveLaborAgreement"}, "percentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "periodicReservationPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "referencePayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "resultPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "startEmployeeAge": {"key": 1, "value": "CollectiveLaborAgreement"}, "startEmployeeAgeType": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "BFC_CLA_1_2", "endEmployeeAge": 0.5, "endEmployeeAgeType": {"key": 2, "value": "Eerste van de periode NA bereiken leeftijd"}, "financialMarkupPercentage": 2e-06, "financialReservationPercentage": 2e-06, "id": "000009a8-07e9-0001-0200-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": true, "isPartTimeCalculation": false, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": false, "key": 1, "minimumMaximumType": {"key": 2, "value": "Resultaat"}, "payoutPayrollPeriod": {"periodNumber": 3}, "payslipType": {"key": 4, "value": "Normale strook"}, "percentage": 0.110002, "periodicReservationPayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "UREN GEWERKT", "key": 1}, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 6, "value": "Eenheden"}, "description": "KM.ONBELAST", "key": 41}, "startEmployeeAge": 0.25, "startEmployeeAgeType": {"key": 1, "value": "Eerste van de periode VAN bereiken leeftijd"}, "startPayrollPeriod": {"payrollPeriodId": 202502, "periodEndDate": "2025-02-28", "periodNumber": 2, "periodStartDate": "2025-02-01", "year": 2025}, "year": 2025}, {"accrualEndPayrollPeriod": {"periodNumber": 2}, "advancePayrollComponent": {"category": {"key": 9, "value": "Netto betaling"}, "description": "REISKST ONBEL", "key": 367}, "advancePayrollPeriod": {"periodNumber": 1}, "advancePercentage": 1.101011, "baseType": null, "calculationPayrollPeriod": {"periodNumber": 1}, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseType": {"key": 1, "value": "CollectiveLaborAgreement"}, "calculationPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "endEmployeeAge": {"key": 1, "value": "CollectiveLaborAgreement"}, "endEmployeeAgeType": {"key": 1, "value": "CollectiveLaborAgreement"}, "financialMarkupPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "financialReservationPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isAutomaticCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isCumulativeCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPartTimeCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayoutAtEndOfEmployment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayoutAtStartOfEmployment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isSupplementingDailyWage": {"key": 1, "value": "CollectiveLaborAgreement"}, "minimumMaximumType": {"key": 1, "value": "CollectiveLaborAgreement"}, "payoutPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "payslipType": {"key": 1, "value": "CollectiveLaborAgreement"}, "percentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "periodicReservationPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "referencePayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "resultPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "startEmployeeAge": {"key": 1, "value": "CollectiveLaborAgreement"}, "startEmployeeAgeType": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "BFC_CLA_1_1", "endEmployeeAge": 0.0, "endEmployeeAgeType": null, "financialMarkupPercentage": 1.000111, "financialReservationPercentage": 1.000001, "id": "000009a8-07e9-0001-0100-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": true, "isPartTimeCalculation": false, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": false, "key": 1, "minimumMaximumType": {"key": 2, "value": "Resultaat"}, "payoutPayrollPeriod": {"periodNumber": 2}, "payslipType": {"key": 4, "value": "Normale strook"}, "percentage": 1.111111, "periodicReservationPayrollComponent": {"category": {"key": 6, "value": "Eenheden"}, "description": "KM.ONBELAST", "key": 41}, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 9, "value": "Netto betaling"}, "description": "ONKSTVRG.ONBEL", "key": 366}, "startEmployeeAge": 0.0, "startEmployeeAgeType": null, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 6, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}