{"_embedded": [{"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "BEREKEN GRSL4", "hoursIndication": null, "id": "000009a8-07e9-00c1-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 193, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 26, "value": "Output"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "MUTATIE GRSL4", "hoursIndication": null, "id": "000009a8-07e9-00d5-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 213, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 1, "value": "<PERSON><PERSON>"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseForCalculationBter": {"key": 1, "value": "CollectiveLaborAgreement"}, "category": {"key": 1, "value": "CollectiveLaborAgreement"}, "column": {"key": 1, "value": "CollectiveLaborAgreement"}, "costsEmployer": {"key": 1, "value": "CollectiveLaborAgreement"}, "deductionOrPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "hoursIndication": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageSupplement": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageZw": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isFullTime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isNetToGross": {"key": 1, "value": "CollectiveLaborAgreement"}, "isOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isTravelExpense": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentDescription": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "socialSecurityLiable": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrinting": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrintingAccumulations": {"key": 1, "value": "CollectiveLaborAgreement"}, "taxLiable": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "VERLOONDE UREN", "hoursIndication": null, "id": "000009a8-07e9-01f2-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 498, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 26, "value": "Output"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "DEELTIJD GRSL4", "hoursIndication": null, "id": "000009a8-07e9-027c-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 636, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "CUM GRSL4", "hoursIndication": null, "id": "000009a8-07e9-0280-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 640, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": true, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "CUM PRES GRSL4", "hoursIndication": null, "id": "000009a8-07e9-0284-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 644, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": true, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "CUM DT GRSL4", "hoursIndication": null, "id": "000009a8-07e9-0288-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 648, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": true, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "RES.FIN.GRSL4", "hoursIndication": null, "id": "000009a8-07e9-028c-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 652, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 2, "value": "Credit"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "AAN RES.GRSL4", "hoursIndication": null, "id": "000009a8-07e9-0290-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 656, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "OPSL.KST.GRSL4", "hoursIndication": null, "id": "000009a8-07e9-0294-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 660, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 2, "value": "Credit"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "AAN OPSL.GRSL4", "hoursIndication": null, "id": "000009a8-07e9-0298-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 664, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "TE BET GRSL4", "hoursIndication": null, "id": "000009a8-07e9-0346-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 838, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "UIT BET GRSL4 ", "hoursIndication": null, "id": "000009a8-07e9-0350-0000-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 848, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 13, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}