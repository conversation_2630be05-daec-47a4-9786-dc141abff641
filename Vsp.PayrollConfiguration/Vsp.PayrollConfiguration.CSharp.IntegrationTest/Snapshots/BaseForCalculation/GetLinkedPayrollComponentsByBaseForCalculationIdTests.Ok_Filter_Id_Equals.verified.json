{"_embedded": [{"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 26, "value": "Output"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseForCalculationBter": {"key": 1, "value": "CollectiveLaborAgreement"}, "category": {"key": 1, "value": "CollectiveLaborAgreement"}, "column": {"key": 1, "value": "CollectiveLaborAgreement"}, "costsEmployer": {"key": 1, "value": "CollectiveLaborAgreement"}, "deductionOrPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "hoursIndication": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageSupplement": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageZw": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isFullTime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isNetToGross": {"key": 1, "value": "CollectiveLaborAgreement"}, "isOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isTravelExpense": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentDescription": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "socialSecurityLiable": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrinting": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrintingAccumulations": {"key": 1, "value": "CollectiveLaborAgreement"}, "taxLiable": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "MUT.  VAKTSL", "hoursIndication": null, "id": "000009a6-07e9-00d2-0000-000000000000", "inheritanceLevel": {"id": "1c0fd70a-be48-42d5-95a9-d1536b643d42", "type": {"key": 1, "value": "CollectiveLaborAgreement"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 210, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}