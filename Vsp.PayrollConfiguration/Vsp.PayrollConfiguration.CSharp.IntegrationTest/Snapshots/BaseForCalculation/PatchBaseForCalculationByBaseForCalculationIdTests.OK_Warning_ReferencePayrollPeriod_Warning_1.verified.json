{"content": {"accrualEndPayrollPeriod": null, "advancePayrollComponent": null, "advancePayrollPeriod": null, "advancePercentage": 0.0, "baseType": null, "calculationPayrollPeriod": {"periodNumber": 1}, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseType": {"key": 1, "value": "CollectiveLaborAgreement"}, "calculationPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "endEmployeeAge": {"key": 1, "value": "CollectiveLaborAgreement"}, "endEmployeeAgeType": {"key": 1, "value": "CollectiveLaborAgreement"}, "financialMarkupPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "financialReservationPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isAutomaticCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isCumulativeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPartTimeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtEndOfEmployment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayoutAtStartOfEmployment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isSupplementingDailyWage": {"key": 1, "value": "CollectiveLaborAgreement"}, "minimumMaximumType": {"key": 1, "value": "CollectiveLaborAgreement"}, "payoutPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "payslipType": {"key": 1, "value": "CollectiveLaborAgreement"}, "percentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "periodicReservationPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "referencePayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "resultPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "startEmployeeAge": {"key": 1, "value": "CollectiveLaborAgreement"}, "startEmployeeAgeType": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "EMPTY", "endEmployeeAge": 0.0, "endEmployeeAgeType": null, "financialMarkupPercentage": 0.0, "financialReservationPercentage": 0.0, "id": "000009ba-07e9-0001-0100-000000000000", "inheritanceLevel": {"id": "fab89249-85ab-4c5e-b391-0a1eeb5b2a8c", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": false, "isPartTimeCalculation": true, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": false, "key": 1, "minimumMaximumType": {"key": 2, "value": "Resultaat"}, "payoutPayrollPeriod": {"periodNumber": 1}, "payslipType": {"key": 4, "value": "Normale strook"}, "percentage": 0.0, "periodicReservationPayrollComponent": null, "referencePayrollPeriod": {"periodNumber": 2}, "resultPayrollComponent": {"category": {"key": 98, "value": "Intern"}, "description": "BRUTO", "key": 257}, "startEmployeeAge": 0.0, "startEmployeeAgeType": null, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, "messages": [{"code": 0, "description": "Note! The reference payroll period lies in the PREVIOUS year.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_1", "messageType": 4, "properties": null, "type": "Warning"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}