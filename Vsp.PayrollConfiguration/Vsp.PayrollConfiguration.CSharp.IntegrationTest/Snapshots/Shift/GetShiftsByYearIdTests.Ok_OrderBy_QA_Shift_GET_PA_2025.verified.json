{"_embedded": [{"bonusPercentage": 42.0, "definedAtLevel": {"bonusPercentage": {"key": 3, "value": "PayrollAdministration"}, "fullTimeHoursPerWeek": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}}, "fullTimeHoursPerWeek": 42.0, "id": "0000094e-07e9-0004-0200-000000000000", "inheritanceLevel": {"id": "ef87f167-a7b7-4ee5-8d33-fb387737f39b", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 4, "startPayrollPeriod": {"payrollPeriodId": 202502, "periodEndDate": "2025-02-28", "periodNumber": 2, "periodStartDate": "2025-02-01", "year": 2025}, "year": 2025}, {"bonusPercentage": 34.25, "definedAtLevel": {"bonusPercentage": {"key": 3, "value": "PayrollAdministration"}, "fullTimeHoursPerWeek": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}}, "fullTimeHoursPerWeek": 34.25, "id": "0000094e-07e9-0004-0100-000000000000", "inheritanceLevel": {"id": "ef87f167-a7b7-4ee5-8d33-fb387737f39b", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 4, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, {"bonusPercentage": 33.25, "definedAtLevel": {"bonusPercentage": {"key": 3, "value": "PayrollAdministration"}, "fullTimeHoursPerWeek": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}}, "fullTimeHoursPerWeek": 33.25, "id": "0000094e-07e9-0003-0100-000000000000", "inheritanceLevel": {"id": "ef87f167-a7b7-4ee5-8d33-fb387737f39b", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 3, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, {"bonusPercentage": 22.25, "definedAtLevel": {"bonusPercentage": {"key": 2, "value": "WageModel"}, "fullTimeHoursPerWeek": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}}, "fullTimeHoursPerWeek": 22.25, "id": "0000094e-07e9-0002-0100-000000000000", "inheritanceLevel": {"id": "ef87f167-a7b7-4ee5-8d33-fb387737f39b", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 2, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, {"bonusPercentage": 11.25, "definedAtLevel": {"bonusPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "fullTimeHoursPerWeek": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}}, "fullTimeHoursPerWeek": 11.25, "id": "0000094e-07e9-0001-0100-000000000000", "inheritanceLevel": {"id": "ef87f167-a7b7-4ee5-8d33-fb387737f39b", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 1, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 5, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}