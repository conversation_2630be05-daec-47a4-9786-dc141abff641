{"content": {"bonusPercentage": 0.0, "definedAtLevel": {"bonusPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "fullTimeHoursPerWeek": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}}, "fullTimeHoursPerWeek": 40.0, "id": "00000951-07e9-0001-0100-000000000000", "inheritanceLevel": {"id": "7e77d3f6-cf02-424b-9879-7d0f22198156", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 1, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}