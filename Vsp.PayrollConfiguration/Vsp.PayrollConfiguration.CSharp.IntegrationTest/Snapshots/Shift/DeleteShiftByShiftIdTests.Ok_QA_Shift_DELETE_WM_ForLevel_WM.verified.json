{"_embedded": [{"bonusPercentage": 0.125, "definedAtLevel": {"bonusPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "fullTimeHoursPerWeek": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}}, "fullTimeHoursPerWeek": 1.25, "id": "0000095b-07e9-0001-0100-000000000000", "inheritanceLevel": {"id": "2a4f68fe-3dcf-40e3-8ed1-24cac87ecf9a", "type": {"key": 2, "value": "WageModel"}}, "shiftNumber": 1, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}