{"content": {"aof": {"key": 1, "value": "Kleine werkgever"}, "dateAvailableEss": null, "dateEssMail": null, "definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 1, "value": "CollectiveLaborAgreement"}, "standardShift": {"key": 2, "value": "WageModel"}}, "id": "00000947-07e9-0000-0000-000000000000", "inheritanceLevel": {"id": "92a092b0-d6c9-46dc-8d88-ce006cfbffb4", "type": {"key": 3, "value": "PayrollAdministration"}}, "payrollPeriodType": {"key": 1, "value": "<PERSON><PERSON>"}, "sendEssMail": false, "standardEmployeeProfile": null, "standardShift": null, "testYear": false, "year": 2025, "yearTransition": {"isPerformed": false, "isRequested": false, "performedDate": null}, "zwSelfInsurerStartPayrollPeriod": null}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}