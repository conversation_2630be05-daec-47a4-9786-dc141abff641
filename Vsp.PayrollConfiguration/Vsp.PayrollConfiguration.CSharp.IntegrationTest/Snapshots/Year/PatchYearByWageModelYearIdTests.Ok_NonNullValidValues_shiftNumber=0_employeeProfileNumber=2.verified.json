{"content": {"definedAtLevel": {"payrollPeriodType": {"key": 1, "value": "CollectiveLaborAgreement"}, "standardEmployeeProfile": {"key": 2, "value": "WageModel"}, "standardShift": {"key": 2, "value": "WageModel"}}, "id": "00000946-07e4-0000-0000-000000000000", "inheritanceLevel": {"id": "831a15c4-51db-45db-8599-d63b4e6c0685", "type": {"key": 2, "value": "WageModel"}}, "payrollPeriodType": {"key": 1, "value": "<PERSON><PERSON>"}, "standardEmployeeProfile": {"description": "WM employee profile 2 2020", "employeeProfileNumber": 2}, "standardShift": null, "year": 2020}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}