{"_embedded": [{"age": 50, "baseForCalculation": {"key": 1}, "definedAtLevel": {"id": {"key": 2, "value": "WageModel"}, "minimum": {"key": 2, "value": "WageModel"}}, "id": "000009dd-07e9-0001-3200-0a0000000000", "inheritanceLevel": {"id": "2b24b279-c6d5-4f0e-aa0f-593d694210a1", "type": {"key": 2, "value": "WageModel"}}, "minimum": 50.0, "startPayrollPeriod": {"payrollPeriodId": 202510, "periodEndDate": "2025-10-31", "periodNumber": 10, "periodStartDate": "2025-10-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}