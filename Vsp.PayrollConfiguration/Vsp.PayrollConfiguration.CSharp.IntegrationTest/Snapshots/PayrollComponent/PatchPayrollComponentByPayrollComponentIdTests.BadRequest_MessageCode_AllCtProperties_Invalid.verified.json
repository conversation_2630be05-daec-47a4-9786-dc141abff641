{"content": null, "messages": [{"code": 0, "description": "deductionOrPayment.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "paymentPeriod.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_PaymentPeriod_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "taxLiable.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_TaxLiable_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "socialSecurityLiable.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "hoursIndication.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_HoursIndication_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "costsEmployer.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_CostsEmployer_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "baseForCalculationBter.key is invalid", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_BaseForCalculationBter_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}