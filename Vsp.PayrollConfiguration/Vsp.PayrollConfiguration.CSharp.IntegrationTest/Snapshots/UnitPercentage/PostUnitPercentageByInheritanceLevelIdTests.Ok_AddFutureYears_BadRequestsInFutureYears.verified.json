{"content": {"calculateOver": {"key": 2, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "definedAtLevel": {"calculateOver": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "percentage": {"key": 3, "value": "PayrollAdministration"}}, "id": "Guid_1", "inheritanceLevel": {"id": "Guid_2", "type": {"key": 3, "value": "PayrollAdministration"}}, "payrollComponent": {"category": {"key": 6, "value": "Eenheden"}, "description": "PA UNIT 46", "key": 46}, "percentage": 8.9, "startPayrollPeriod": {"payrollPeriodId": 202301, "periodEndDate": "2023-01-31", "periodNumber": 1, "periodStartDate": "2023-01-01", "year": 2023}, "year": 2023}, "messages": [{"code": 0, "description": "Failed to automatically add entity to future year(s) as well. See properties for details.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Entity_NotAddedToFutureYear", "messageType": 4, "properties": "[2024, 2025]", "type": "Warning"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}