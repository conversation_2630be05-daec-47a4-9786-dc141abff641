{"content": null, "messages": [{"code": 0, "description": "Year does not exist on current inheritance level.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Year_DoesNotExist", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "payrollComponent.key is invalid for unit percentage: it doesn't exist on current inheritance level or doesn't have category 6 (unit).", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_UnitPercentage_PayrollComponent_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}