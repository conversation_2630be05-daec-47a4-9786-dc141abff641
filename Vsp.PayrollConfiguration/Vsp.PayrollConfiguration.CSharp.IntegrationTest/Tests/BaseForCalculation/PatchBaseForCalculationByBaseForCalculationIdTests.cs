using Vsp.PayrollConfiguration.BaseForCalculation.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Mappers;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Mappers;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculation;

[Collection(EntityNames.BaseForCalculation)]
public class PatchBaseForCalculationByBaseForCalculationIdTests(
    WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture, [new BaseForCalculationProfile(), new SharedProfile()])
{
    protected override string FolderName => "BaseForCalculation";
    protected override bool UseTransaction => true;

    private const string baseForCalculationIdCLA = "000009b8-07e9-0001-0100-000000000000";
    private const string baseForCalculationIdWM = "000009b9-07e9-0001-0100-000000000000";
    private const string baseForCalculationIdPA = "000009ba-07e9-0001-0100-000000000000";


    #region Validations Tests - OK

    [Theory]
    [InlineData(baseForCalculationIdCLA)]
    [InlineData(baseForCalculationIdWM)]
    [InlineData(baseForCalculationIdPA)]
    public async Task Ok_WithIsAutomaticCalculationTrue(string baseForCalculationId) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationId),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel((patchModel) =>
                {
                    patchModel.Description = "New description";
                    patchModel.BaseType = new() { Key = 1 };
                    patchModel.StartEmployeeAgeType = new() { Key = 1 };
                    patchModel.StartEmployeeAge = 21.0m;
                    patchModel.EndEmployeeAgeType = new() { Key = 1 };
                    patchModel.EndEmployeeAge = 45.0m;
                    patchModel.ResultPayrollComponent = new() { Key = 260 };
                    patchModel.Percentage = 12.5m;
                    patchModel.PayslipType = new(){ Key = 5 };
                    patchModel.IsCumulativeCalculation = false;
                    patchModel.IsAutomaticCalculation = true;
                    patchModel.IsSupplementingDailyWage = true;
                    patchModel.MinimumMaximumType = new() { Key = 1 };
                }),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Theory]
    [InlineData(baseForCalculationIdCLA)]
    [InlineData(baseForCalculationIdWM)]
    [InlineData(baseForCalculationIdPA)]
    public async Task Ok_WithIsPartTimeCalculationTrue(string baseForCalculationId) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationId),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel((patchModel) =>
                {
                    patchModel.ReferencePayrollPeriod = new() { PeriodNumber = 2 };
                    patchModel.IsCumulativeCalculation = false;
                    patchModel.IsPartTimeCalculation = true;
                }),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    // AccrualEndPayrollPeriod and IsPayoutAtStartOfEmployment properties are removed from this test because their
    // edition always causes warning. Their edition is tested along with warning rules 2 and 3.
    [Theory]
    [InlineData(baseForCalculationIdCLA)]
    [InlineData(baseForCalculationIdWM)]
    [InlineData(baseForCalculationIdPA)]
    public async Task Ok_WithIsCumulativeCalculationTrue(string baseForCalculationId) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationId),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel((patchModel) =>
                {
                    patchModel.CalculationPayrollPeriod = new() { PeriodNumber = 1 };
                    patchModel.PayoutPayrollPeriod = new() { PeriodNumber = 3 };
                    patchModel.IsPayoutAtEndOfEmployment = true;
                    patchModel.AdvancePayrollComponent = new() { Key = 210 };
                    patchModel.AdvancePercentage = 5.0m;
                    patchModel.AdvancePayrollPeriod = new() { PeriodNumber = 2 };
                    patchModel.PeriodicReservationPayrollComponent = new() { Key = 190 };
                    patchModel.FinancialReservationPercentage = 2.5m;
                    patchModel.FinancialMarkupPercentage = 1.5m;
                }),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    #endregion

    #region OK - Warnings

    [Fact] // Rule 1
    public async Task OK_Warning_PayoutPayrollPeriod_Warning_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PayoutPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 1 };
                    m.CalculationPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 2 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.OK, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 1 combined with X-ValidateOnly
    public async Task OK_Warning_PayoutPayrollPeriod_Warning_1_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PayoutPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 1 };
                    m.CalculationPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 2 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.OK, BodyValidation = JsonBodyValidation.Default }
        );

        // Check database to make sure base for calculation was not modified
        using var loketContext = GetLoketContext();
        var baseForCalculation = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.BaseForCalculation>(Guid.Parse(baseForCalculationIdPA)))
            .SingleAsync();
        baseForCalculation.PayoutPayrollPeriodNumber.Should().Be(0);
        baseForCalculation.CalculationPayrollPeriodNumber.Should().Be(0);
    }

    [Fact] // Rules 2, Rule 3, and AccrualEndPayrollPeriod + IsPayoutAtStartOfEmployment successful edition
    public async Task OK_Warning_AccrualEndPayrollPeriod_Warning_1_and_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AccrualEndPayrollPeriod = new() { PeriodNumber = 1 };
                    m.CalculationPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 2 };
                    m.IsPayoutAtStartOfEmployment = true;
                    // to avoid other rules
                    m.PayoutPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 3 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.OK, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 4
    public async Task OK_Warning_ReferencePayrollPeriod_Warning_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.ReferencePayrollPeriod = new() { PeriodNumber = 2 };
                    m.CalculationPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 1 };
                    // to avoid other rules
                    m.PayoutPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 1 };
                    m.IsPartTimeCalculation = true;
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.OK, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 5
    public async Task OK_Warning_ReferencePayrollPeriod_Warning_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.ReferencePayrollPeriod = null;
                    m.IsPartTimeCalculation = true;
                    // to avoid other rules
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.OK, BodyValidation = JsonBodyValidation.Default }
        );

    #endregion

    #region BadRequest - ModelStateValidation

    [Fact]
    public async Task BadRequest_ModelValidation_NoPatchModel()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = null,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_MissingProperties()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = new object(),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_DefaultValueProperties()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationPatchModel(),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_Description_MaxLength()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.Description = new string('A', 51)),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-0.1)]
    [InlineData(999.76)]
    public async Task BadRequest_ModelValidation_StartEmployeeAge_OutOfRange(decimal startEmployeeAge)
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.StartEmployeeAge = startEmployeeAge),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_StartEmployeeAge_Regex()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.StartEmployeeAge = 1.13m),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-0.1)]
    [InlineData(999.76)]
    public async Task BadRequest_ModelValidation_EndEmployeeAge_OutOfRange(decimal endEmployeeAge)
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.EndEmployeeAge = endEmployeeAge),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_EndEmployeeAge_Regex()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.EndEmployeeAge = 1.13m),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-0.000001)]
    [InlineData(1000)]
    public async Task BadRequest_ModelValidation_Percentage_OutOfRange(decimal percentage)
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.Percentage = percentage),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_Percentage_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.Percentage = 5.1234567m),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-0.000001)]
    [InlineData(1000)]
    public async Task BadRequest_ModelValidation_AdvancePercentage_OutOfRange(decimal advancePercentage)
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.AdvancePercentage = advancePercentage),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_AdvancePercentage_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.AdvancePercentage = 5.1234567m),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-0.000001)]
    [InlineData(1000)]
    public async Task BadRequest_ModelValidation_FinancialReservationPercentage_OutOfRange(decimal financialReservationPercentage)
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.FinancialReservationPercentage = financialReservationPercentage),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_FinancialReservationPercentage_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.FinancialReservationPercentage = 3.1234567m),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-0.000001)]
    [InlineData(1000)]
    public async Task BadRequest_ModelValidation_FinancialMarkupPercentage_OutOfRange(decimal financialMarkupPercentage)
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.FinancialMarkupPercentage = financialMarkupPercentage),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_FinancialMarkupPercentage_TooManyDecimals()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m => m.FinancialMarkupPercentage = 1.1234567m),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    #endregion

    #region BadRequest - MessageCodes

    // Stage 1

    [Fact]
    public async Task BadRequest_MessageCode_Stage_1() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel((patchModel) =>
                {
                    patchModel.BaseType = new() { Key = 150 };
                    patchModel.StartEmployeeAgeType = new() { Key = 600 };
                    patchModel.EndEmployeeAgeType = new() { Key = 689 };
                    patchModel.ResultPayrollComponent = new() { Key = 2000 };
                    patchModel.PayslipType = new() { Key = 667 };
                    patchModel.CalculationPayrollPeriod = new() { PeriodNumber = 53 };
                    patchModel.ReferencePayrollPeriod = new() { PeriodNumber = 53 };
                    patchModel.PayoutPayrollPeriod = new() { PeriodNumber = 53 };
                    patchModel.AccrualEndPayrollPeriod = new() { PeriodNumber = 53 };
                    patchModel.AdvancePayrollComponent = new() { Key = 2001 };
                    patchModel.AdvancePayrollPeriod = new() { PeriodNumber = 53 };
                    patchModel.PeriodicReservationPayrollComponent = new() { Key = 2002 };
                    patchModel.MinimumMaximumType = new() { Key = 698 };
                }),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    // Stage 2

    [Fact] // Rule 1
    public async Task BadRequest_MessageCode_BaseType_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.BaseType = new() { Key = 1 };
                    m.FinancialReservationPercentage = 0;
                    m.PeriodicReservationPayrollComponent = null;
                    m.IsAutomaticCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 2
    public async Task BadRequest_MessageCode_StartEmployeeAge_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.StartEmployeeAge = 0;
                    m.StartEmployeeAgeType = new() { Key = 1 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 3
    public async Task BadRequest_MessageCode_StartEmployeeAge_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.StartEmployeeAge = 10;
                    m.StartEmployeeAgeType = null;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 4
    public async Task BadRequest_MessageCode_EndEmployeeAge_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.EndEmployeeAge = 0;
                    m.EndEmployeeAgeType = new() { Key = 1 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 5
    public async Task BadRequest_MessageCode_EndEmployeeAge_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.EndEmployeeAge = 10;
                    m.EndEmployeeAgeType = null;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    // Rule 6 - discarded

    [Fact] // Rule 7
    public async Task BadRequest_MessageCode_ResultPayrollComponent_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.ResultPayrollComponent = new() { Key = 319 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 8
    public async Task BadRequest_MessageCode_CalculationPayrollPeriod_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.CalculationPayrollPeriod = new() { PeriodNumber = 1 };
                    m.IsAutomaticCalculation = true;
                    //to avoid other rules
                    m.PayoutPayrollPeriod = new() { PeriodNumber = 1 };
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 9
    public async Task BadRequest_MessageCode_CalculationPayrollPeriod_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.CalculationPayrollPeriod = new() { PeriodNumber = 1 };
                    m.PayoutPayrollPeriod = null;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 10
    public async Task BadRequest_MessageCode_ReferencePayrollPeriod_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.ReferencePayrollPeriod = new() { PeriodNumber = 1 };
                    m.IsPartTimeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 11
    public async Task BadRequest_MessageCode_PayoutPayrollPeriod_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PayoutPayrollPeriod = null;
                    m.AdvancePayrollPeriod = new() { PeriodNumber = 1 };
                    // to avoid other rules
                    m.AdvancePayrollComponent = new() { Key = 210 };
                    m.AdvancePercentage = 5m;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 12
    public async Task BadRequest_MessageCode_PayoutPayrollPeriod_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PayoutPayrollPeriod = null;
                    m.IsPayoutAtEndOfEmployment = true;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 13
    public async Task BadRequest_MessageCode_PayoutPayrollPeriod_3()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PayoutPayrollPeriod = new() { PeriodNumber = 2 };
                    m.AdvancePayrollPeriod = new() { PeriodNumber = 2 };
                    // to avoid other rules
                    m.AdvancePayrollComponent = new() { Key = 210 };
                    m.AdvancePercentage = 5m;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 14
    public async Task BadRequest_MessageCode_PayoutPayrollPeriod_4()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PayoutPayrollPeriod = new() { PeriodNumber = 1 };
                    m.IsCumulativeCalculation = false; 
                    // to avoid other rules
                    m.IsAutomaticCalculation = true;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 14 - term calculation
    public async Task BadRequest_MessageCode_PayoutPayrollPeriod_4_termCalculation()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PayoutPayrollPeriod = new() { PeriodNumber = 1 };
                    m.IsCumulativeCalculation = false; 
                    m.IsAutomaticCalculation = false;
                    m.IsPartTimeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 15
    public async Task BadRequest_MessageCode_AccrualEndPayrollPeriod_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AccrualEndPayrollPeriod = new() { PeriodNumber = 1 };
                    m.IsPartTimeCalculation = false;
                    m.IsCumulativeCalculation = false;
                    // to avoid other rules
                    m.IsAutomaticCalculation = true;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 16
    public async Task BadRequest_MessageCode_AccrualEndPayrollPeriod_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AccrualEndPayrollPeriod = new() { PeriodNumber = 2 };
                    m.CalculationPayrollPeriod = new() { PeriodNumber = 2 };
                    // to avoid other rules
                    m.PayoutPayrollPeriod = new() { PeriodNumber = 2 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 17
    public async Task BadRequest_MessageCode_IsPayoutAtStartOfEmployment_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AccrualEndPayrollPeriod = null;
                    m.IsPayoutAtStartOfEmployment = true;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 18
    public async Task BadRequest_MessageCode_IsPayoutAtEndOfEmployment_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.IsPayoutAtEndOfEmployment = true;
                    m.IsAutomaticCalculation = true;
                    // to avoid other rules
                    m.PayoutPayrollPeriod = new() { PeriodNumber = 1 };
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 18 - term calculation
    public async Task BadRequest_MessageCode_IsPayoutAtEndOfEmployment_1_termCalculation()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.IsPayoutAtEndOfEmployment = true;
                    m.IsAutomaticCalculation = false;
                    m.IsPartTimeCalculation = false;
                    m.IsCumulativeCalculation = false;
                    // to avoid other rules
                    m.PayoutPayrollPeriod = new() { PeriodNumber = 1 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 19
    public async Task BadRequest_MessageCode_AdvancePayrollComponent_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AdvancePayrollComponent = new() { Key = 260 };
                    m.IsAutomaticCalculation = true;
                    // to avoid other rules
                    m.AdvancePercentage = 5m;
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    // Rule 20 - discarded

    [Fact] // Rule 21
    public async Task BadRequest_MessageCode_AdvancePayrollComponent_3()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AdvancePayrollComponent = null;
                    m.AdvancePercentage = 5;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 22
    public async Task BadRequest_MessageCode_AdvancePayrollComponent_4()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AdvancePayrollComponent = new() { Key = 319 };
                    // to avoid other rules
                    m.AdvancePercentage = 5;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 23
    public async Task BadRequest_MessageCode_AdvancePercentage_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AdvancePercentage = 0;
                    m.AdvancePayrollComponent = new() { Key = 260 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 24
    public async Task BadRequest_MessageCode_AdvancePayrollPeriod_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AdvancePayrollPeriod = new() { PeriodNumber = 1 };
                    m.AdvancePayrollComponent = null;
                    // to avoid other rules
                    m.PayoutPayrollPeriod = new() { PeriodNumber = 2 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 25
    public async Task BadRequest_MessageCode_PeriodicReservationPayrollComponent_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PeriodicReservationPayrollComponent = new() { Key = 260 };
                    m.IsAutomaticCalculation = true;
                    // to avoid other rules
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    // Rule 26 - discarded

    [Fact] // Rule 27
    public async Task BadRequest_MessageCode_PeriodicReservationPayrollComponent_3()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PeriodicReservationPayrollComponent = new() { Key = 319 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 28
    public async Task BadRequest_MessageCode_PeriodicReservationPayrollComponent_4()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.PeriodicReservationPayrollComponent = new() { Key = 260 };
                    m.IsCumulativeCalculation = false;
                    // to avoid other rules
                    m.IsPartTimeCalculation = true;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 29
    public async Task BadRequest_MessageCode_FinancialReservationPercentage_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.FinancialReservationPercentage = 1;
                    m.IsAutomaticCalculation = true;
                    // to avoid other rules
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 30
    public async Task BadRequest_MessageCode_FinancialMarkupPercentage_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.FinancialMarkupPercentage = 1;
                    m.FinancialReservationPercentage = 0;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 31
    public async Task BadRequest_MessageCode_FinancialMarkupPercentage_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.FinancialMarkupPercentage = 1;
                    m.IsAutomaticCalculation = true;
                    // to avoid other rules
                    m.FinancialReservationPercentage = 2;
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 32 - IsCumulativeCalculation
    public async Task BadRequest_MessageCode_IsPartTimeCalculation_1_With_IsCumulativeCalculation()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.IsPartTimeCalculation = true;
                    m.IsCumulativeCalculation = true;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 32 - IsAutomaticCalculation
    public async Task BadRequest_MessageCode_IsPartTimeCalculation_1_With_IsAutomaticCalculation()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.IsPartTimeCalculation = true;
                    m.IsAutomaticCalculation = true;
                    // to avoid other rules
                    m.IsCumulativeCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 33
    public async Task BadRequest_MessageCode_IsAutomaticCalculation_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.IsAutomaticCalculation = true;
                    m.IsCumulativeCalculation = true;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 34
    public async Task BadRequest_MessageCode_IsAutomaticCalculation_2()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.IsAutomaticCalculation = false;
                    m.ResultPayrollComponent = new() { Key = 182 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 35
    public async Task BadRequest_MessageCode_IsAutomaticCalculation_3()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.IsAutomaticCalculation = false;
                    m.ResultPayrollComponent = new() { Key = 184 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 36
    public async Task BadRequest_MessageCode_BaseForCalculation_1()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.IsCumulativeCalculation = false;
                    m.IsPartTimeCalculation = false;
                    m.IsAutomaticCalculation = false;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 37
    public async Task BadRequest_MessageCode_ResultPayrollComponent_AdvancePayrollComponent_Equal()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.ResultPayrollComponent = new() { Key = 257 };
                    m.AdvancePayrollComponent = new() { Key = 257 };
                    // to avoid other rules
                    m.AdvancePercentage = 1;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 38
    public async Task BadRequest_MessageCode_ResultPayrollComponent_PeriodicReservationPayrollComponent_Equal()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.ResultPayrollComponent = new() { Key = 257 };
                    m.PeriodicReservationPayrollComponent = new() { Key = 257 };
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    [Fact] // Rule 39
    public async Task BadRequest_MessageCode_AdvancePayrollComponent_PeriodicReservationPayrollComponent_Equal()
        => await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PatchBaseForCalculationByBaseForCalculationIdAsync}".Replace("{baseForCalculationId:guid}", baseForCalculationIdPA),
                Method = HttpMethod.Patch,
                Body = GetBaseForCalculationPatchModel(m =>
                {
                    // this rule error conditions
                    m.AdvancePayrollComponent = new() { Key = 260 };
                    m.PeriodicReservationPayrollComponent = new() { Key = 260 };
                    // to avoid other rules
                    m.AdvancePercentage = 1;
                }),
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default }
        );

    #endregion

    #region Patch model generation

    private BaseForCalculationPatchModel GetBaseForCalculationPatchModel(Action<BaseForCalculationPatchModel>? setNewPropertyValues = null)
    {
        var patchModel = new BaseForCalculationPatchModel
        {
            Description = "EMPTY",
            BaseType = null,
            StartEmployeeAgeType = null,
            StartEmployeeAge = 0.0m,
            EndEmployeeAgeType = null,
            EndEmployeeAge = 0.0m,
            ResultPayrollComponent = new KeyModel { Key = 257 },
            Percentage = 0.0m,
            CalculationPayrollPeriod = null,
            ReferencePayrollPeriod = null,
            PayoutPayrollPeriod = null,
            AccrualEndPayrollPeriod = null,
            PayslipType = new KeyModel { Key = 4 },
            IsPayoutAtStartOfEmployment = false,
            IsPayoutAtEndOfEmployment = false,
            AdvancePayrollComponent = null,
            AdvancePercentage = 0.0m,
            AdvancePayrollPeriod = null,
            PeriodicReservationPayrollComponent = null,
            FinancialReservationPercentage = 0.0m,
            FinancialMarkupPercentage = 0.0m,
            IsCumulativeCalculation = true,
            IsPartTimeCalculation = false,
            IsAutomaticCalculation = false,
            IsSupplementingDailyWage = false,
            MinimumMaximumType = new KeyModel { Key = 2 }
        };

        setNewPropertyValues?.Invoke(patchModel);

        return patchModel;
    }

    #endregion
}