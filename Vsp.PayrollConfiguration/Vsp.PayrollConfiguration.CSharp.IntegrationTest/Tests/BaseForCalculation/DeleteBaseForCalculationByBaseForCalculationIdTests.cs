using Vsp.PayrollConfiguration.BaseForCalculation.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculation;

[Collection(EntityNames.BaseForCalculation)]
public class DeleteBaseForCalculationByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculation";
    protected override bool UseTransaction => true;


    private static readonly Guid QA_BaseForCalculation_DELETE_PA_Year2025 = Guid.Parse("000009db-07e9-0000-0000-000000000000"); // Year 2025 for PA (Payroll Administration)

    private static readonly Guid QA_BaseForCalculation_DELETE_PA_Deletable_NoChildren = Guid.Parse("000009db-07e9-0006-0100-000000000000"); // Deletable BaseForCalculation for PA with no children - Year 2025

    private static readonly Guid QA_BaseForCalculation_DELETE_CLA_EntityHasChildren = Guid.Parse("000009d9-07e9-0001-0100-000000000000"); // Non-deletable BaseForCalculation for CLA due to dependent child inheritance - Year 2025
    private static readonly Guid QA_BaseForCalculation_DELETE_PA_PayrollPeriod_FirstPeriodCannotBeDeleted = Guid.Parse("000009db-07e9-0007-0100-000000000000"); // Non-deletable BaseForCalculation for PA as it is the first period with data for later periods - Year 2025

    private static readonly Guid QA_BaseForCalculation_DELETE_PA_HasChild_BasePayrollComponent = Guid.Parse("000009db-07e9-0003-0100-000000000000"); // Non-deletable BaseForCalculation for PA with base components - Year 2025
    private static readonly Guid QA_BaseForCalculation_DELETE_PA_HasChild_AgeBasedMinimum = Guid.Parse("000009db-07e9-0004-0100-000000000000"); // Non-deletable BaseForCalculation for PA with base minimum - Year 2025
    private static readonly Guid QA_BaseForCalculation_DELETE_PA_HasChild_AgeBasedMaximum = Guid.Parse("000009db-07e9-0005-0100-000000000000"); // Non-deletable BaseForCalculation for PA with base maximum - Year 2025
    private static readonly Guid QA_BaseForCalculation_DELETE_PA_InUse_EmploymentProfile = Guid.Parse("000009db-07e9-000a-0100-000000000000"); // Non-deletable BaseForCalculation for PA used in employment profile PROFILE_USING_BFC_10 - Year 2025

    #region OK Tests

    [Fact]
    public async Task Ok_QA_BaseForCalculation_DELETE_PA_2025_Deletable_NoChildren()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationRoutes.DeleteBaseForCalculationByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BaseForCalculation_DELETE_PA_Deletable_NoChildren.ToString()),
                Method = HttpMethod.Delete,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });

        var getUri = $"{BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync}".Replace("{yearId:guid}", QA_BaseForCalculation_DELETE_PA_Year2025.ToString()) +
                     $"?filter=id eq '{QA_BaseForCalculation_DELETE_PA_Deletable_NoChildren}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<BaseForCalculationModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    #endregion

    #region BadRequest Tests - Inheritance Validator Errors

    [Fact]
    public async Task BadRequest_MessageCode_EntityHasChildren() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationRoutes.DeleteBaseForCalculationByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BaseForCalculation_DELETE_CLA_EntityHasChildren.ToString()),
                Method = HttpMethod.Delete,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodCannotBeDeleted() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationRoutes.DeleteBaseForCalculationByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BaseForCalculation_DELETE_PA_PayrollPeriod_FirstPeriodCannotBeDeleted.ToString()),
                Method = HttpMethod.Delete,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    #endregion

    #region BadRequest Tests - BaseForCalculation Specific Errors

    [Fact]
    public async Task BadRequest_MessageCode_HasChild_BasePayrollComponent() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationRoutes.DeleteBaseForCalculationByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BaseForCalculation_DELETE_PA_HasChild_BasePayrollComponent.ToString()),
                Method = HttpMethod.Delete,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task BadRequest_MessageCode_HasChild_AgeBasedMinimum() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationRoutes.DeleteBaseForCalculationByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BaseForCalculation_DELETE_PA_HasChild_AgeBasedMinimum.ToString()),
                Method = HttpMethod.Delete,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task BadRequest_MessageCode_HasChild_AgeBasedMaximum() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationRoutes.DeleteBaseForCalculationByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BaseForCalculation_DELETE_PA_HasChild_AgeBasedMaximum.ToString()),
                Method = HttpMethod.Delete,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task BadRequest_MessageCode_InUse_EmploymentProfile() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationRoutes.DeleteBaseForCalculationByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BaseForCalculation_DELETE_PA_InUse_EmploymentProfile.ToString()),
                Method = HttpMethod.Delete,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    #endregion
}
