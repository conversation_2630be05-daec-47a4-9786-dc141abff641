using Vsp.PayrollConfiguration.BaseForCalculation.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculation;

[Collection(EntityNames.BaseForCalculation)]
public class GetBaseForCalculationMetadataByProviderIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture) : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculation";
    protected override bool UseTransaction => false;

    private static readonly string QA_PayrollConfiguration1 = "db6ac336-bcc3-42b7-b40a-b0d25f66c83f";

    [Fact]
    public async Task Ok()
    {
        // Act
        var getUri = BaseForCalculationRoutes.GetBaseForCalculationMetadataByProviderIdAsync.Replace("{providerId:guid}", QA_PayrollConfiguration1);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}