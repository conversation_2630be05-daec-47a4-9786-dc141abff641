using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.PayrollComponent.Constants;
using Vsp.PayrollConfiguration.Repository.Entities;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollComponent;

[Collection(EntityNames.PayrollComponent)]
public class PostPayrollComponentByInheritanceLevelIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollComponent";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_PayrollComponent_POST_CLA_CLASSIC_2025 = Guid.Parse("00000977-07e9-0000-0000-000000000000"); // Year 2025 of QA_PayrollComponent_POST_CLA_CLASSIC

    // Years 2020 to 2025
    private static readonly Guid QA_PayrollComponent_POST_CLA = Guid.Parse("654fc3b7-5eb3-47c2-a285-ec5a888669bf");
    private static readonly Guid QA_PayrollComponent_POST_WM = Guid.Parse("c8c71e3f-27e4-4b6b-abde-63254f24cc53");
    private static readonly Guid QA_PayrollComponent_POST_PA = Guid.Parse("c6415e06-d1ed-4a75-86ba-5415eea6c9fa");

    [Fact]
    public async Task Ok_QA_PayrollComponent_POST_CLA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2025, Key = 2001 },
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_QA_PayrollComponent_POST_WM() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?wageModelId={QA_PayrollComponent_POST_WM}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2025, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_QA_PayrollComponent_POST_PA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?payrollAdministrationId={QA_PayrollComponent_POST_PA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2025, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    #region AddFutureYears

    [Fact]
    public async Task Ok_AddFutureYears_CLA_AddSingle() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2024, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_AddFutureYears_CLA_AddMultiple() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2020, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_AddFutureYears_WM_AddSingle() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?wageModelId={QA_PayrollComponent_POST_WM}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2024, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_AddFutureYears_WM_AddMultiple() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?wageModelId={QA_PayrollComponent_POST_WM}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2020, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_AddFutureYears_PA_AddSingle() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?payrollAdministrationId={QA_PayrollComponent_POST_PA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2024, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_AddFutureYears_PA_AddMultiple() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?payrollAdministrationId={QA_PayrollComponent_POST_PA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2020, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_AddFutureYears_StopWhenEntityExists_CurrentLevel() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?payrollAdministrationId={QA_PayrollComponent_POST_PA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2021, Key = 2002 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = new JsonBodyValidation
                {
                    ScrubGuids = true,
                },
            });

    [Fact]
    public async Task Ok_AddFutureYears_StopWhenEntityExists_ParentLevel() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?payrollAdministrationId={QA_PayrollComponent_POST_PA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2021, Key = 2003 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = new JsonBodyValidation
                {
                    ScrubGuids = true,
                },
            });

    #endregion

    //[Fact]
    public async Task Ok_QA_PayrollComponent_POST_CLA_CLASSIC()
    {
        // Arrange
        var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<Component>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Year, Component>(QA_PayrollComponent_POST_CLA_CLASSIC_2025, x => x.Year))
            .Select(x => x.ComponentId)
            .ToListAsync();
        componentIds.Should().NotBeNullOrEmpty();

        // Act
        var postUri =
            $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}";

        var expectedEntities = new Dictionary<int, ModelComponent?>();
        var insertedEntities = new Dictionary<int, ModelComponent?>();

        var yearId = 2025;
        foreach (var componentId in componentIds)
        {
            var postModel = new PayrollComponentExternalPostModel { Year = 2025, Key = componentId };
            var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel); // Don't check status code - bad requests should be reported later

            // Expected result from QA_PayrollComponent_POST_CLA_CLASSIC
            var expectedEntity = await loketContext.Set<ModelComponent>().AsNoTracking()
                .Where(x => x.InheritanceLevelId == 2423 && x.YearId == yearId && x.ComponentId == componentId)
                .SingleOrDefaultAsync();

            expectedEntities.Add(componentId, expectedEntity);

            // Inserted result from // QA_PayrollComponent_POST_CLA
            var insertedEntity = await loketContext.Set<ModelComponent>().AsNoTracking()
                .Where(x => x.InheritanceLevelId == 2446 && x.YearId == yearId && x.ComponentId == componentId)
                .SingleOrDefaultAsync();

            // HACK: Don't wait for 10 mins! =D
            var failFast = false;
            if (failFast)
            {
                insertedEntity.Should().BeEquivalentTo(expectedEntity, options => options
                    .Excluding(x => x!.Id)
                    .Excluding(x => x!.InheritanceLevel)
                    .Excluding(x => x!.InheritanceLevelId));
            }

            // HACK: Don't compare these properties!
            if (insertedEntity != null && expectedEntity != null)
            {
                insertedEntity.Id = expectedEntity.Id;
                insertedEntity.InheritanceLevelId = expectedEntity.InheritanceLevelId;
            }

            insertedEntities.Add(componentId, insertedEntity);
        }

        // TODO: Correct exclude expression!
        insertedEntities.Should().BeEquivalentTo(expectedEntities, options => options
            .Excluding((IMemberInfo x) => x.DeclaringType == typeof(ModelComponent) && (
                x.Name == nameof(Component.Id) ||
                x.Name == nameof(Component.InheritanceLevelId))));
    }

    #region ModelValidation

    [Fact]
    public async Task BadRequest_ModelValidation_Null() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}",
                Method = HttpMethod.Post,
                Body = ""
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task BadRequest_ModelValidation_Properties_Null() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = null, Key = null }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    #endregion

    #region MessageCodes

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Year_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Year_DoesNotExist() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2019, Key = 2001 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    /// <summary>
    /// Tests transitivity rule of inheritance levels
    /// Simple test to ensure that a component cannot be inserted in PA if it exists in the CLA.
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Key_AlreadyExists_In_PA_DefinedAt_CLA()
    {
        // Arrange: Insert in CLA first
        var request = new Request
        {
            Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}",
            Method = HttpMethod.Post,
            Body = new PayrollComponentExternalPostModel { Year = 2025, Key = 2001 },
        };
        await VerifyCallAsync(
            request,
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
            }
        );

        // Act & Assert: Try to insert the same component in PA
        await VerifyCallAsync(
            request with
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?payrollAdministrationId={QA_PayrollComponent_POST_PA}"
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            }
        );
    }

    /// <summary>
    /// Tests that inserting a payroll component that already exists for the same inheritance level and year should not be allowed.
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_PayrollComponent_Post_Invalid"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Key_AlreadyExists()
    {
        // Arrange
        var postUri = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}";
        var postModel = new PayrollComponentExternalPostModel { Year = 2025, Key = 100 };

        // Act: POST the new payroll component, which should succeed
        await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Act: re-POST the previous payload, which should now fail
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_Key_NonExistent() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollComponentRoutes.PostPayrollComponentByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_PayrollComponent_POST_CLA}",
                Method = HttpMethod.Post,
                Body = new PayrollComponentExternalPostModel { Year = 2025, Key = 1265 }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    #endregion
}