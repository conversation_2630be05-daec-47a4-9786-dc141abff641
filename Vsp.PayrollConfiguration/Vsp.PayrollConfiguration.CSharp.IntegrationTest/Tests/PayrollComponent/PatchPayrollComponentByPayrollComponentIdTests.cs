using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Mappers;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Mappers;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.PayrollComponent.Constants;
using Vsp.PayrollConfiguration.Repository.Entities;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollComponent;

[Collection(EntityNames.PayrollComponent)]
public class PatchPayrollComponentByPayrollComponentIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture, [new PayrollComponentProfile(), new SharedProfile()])
{
    protected override string FolderName => "PayrollComponent";

    protected override bool UseTransaction => true;

    private static readonly string QA_PayrollComponent_PATCH_CLA_2025 = "********-07e9-0000-0000-************";
    private static readonly string QA_PayrollComponent_PATCH_PA_2025 = "********-07e9-0000-0000-************";


    #region ValidationTestsOk

    [Fact] // Description (nl: Naam)
    public async Task Ok_Description()
    {
        // Arrange
        const string componentId = "********-07e9-0104-0000-************"; // ComponentId 260
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.Description = "     TEST 678901234   "; // Leading and trailing spaces should be trimmed

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // DeductionOrPayment (nl: BetalingInhouding) and CostsEmployer (nl: KostenWerkgever)
    [InlineData("********-07e9-0163-0000-************", 1)] // ComponentId 355 - Category 11
    [InlineData("********-07e9-006f-0000-************", 1)] // ComponentId 111 - Category 30
    [InlineData("********-07e9-0314-0000-************", 1)] // ComponentId 788 - SpecialComponent
    [InlineData("********-07e9-0163-0000-************", null)] // ComponentId 355 - Category 11, CostsEmployer null
    [InlineData("********-07e9-006f-0000-************", null)] // ComponentId 111 - Category 30, CostsEmployer null
    [InlineData("********-07e9-0314-0000-************", null)] // ComponentId 788 - SpecialComponent, CostsEmployer null
    public async Task Ok_DeductionOrPayment_and_CostsEmployer(string componentId, int? costsEmployerKey)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.DeductionOrPayment = new KeyModel { Key = 1 };
        payrollComponentPatchModel.CostsEmployer = costsEmployerKey.HasValue ? new KeyModel { Key = costsEmployerKey.Value } : null;

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // IsNetToGross (nl: NettoBruto)
    public async Task Ok_IsNetGross()
    {
        // Arrange
        const string componentId = "********-07e9-01ae-0000-************"; // ComponentId 430
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsNetToGross = true;
        payrollComponentPatchModel.IsBaseForCalculationOvertime = false;
        payrollComponentPatchModel.IsBaseForCalculationDailyWageSupplement = false;
        payrollComponentPatchModel.IsBaseForCalculationDailyWageZw = false;
        payrollComponentPatchModel.BaseForCalculationBter = new KeyModel { Key = 1 };

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);

        var loketContext = GetLoketContext();

        var modelComponent = await loketContext.Set<ModelComponent>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<ModelComponent>(Guid.Parse(componentId)))
            .SingleOrDefaultAsync();
        modelComponent.Should().NotBeNull();
        modelComponent.Description.Should().Be("IsNetGross");
        modelComponent.Category.Should().Be(8);
        modelComponent.GeneralLedgerAccountNumber.Should().BeNull();
        modelComponent.IsLifeSpanScheme.Should().Be(2);
        modelComponent.Order.Should().Be(45);
        modelComponent.IsVisibleByDefault.Should().Be(2);
    }

    [Fact] // IsNetToGross (nl: NettoBruto)
    public async Task Ok_IsNetGross_PA()
    {
        // Arrange
        const string componentId = "********-07e9-01ae-0000-************"; // ComponentId 430
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_PA_2025, componentId);
        payrollComponentPatchModel.IsNetToGross = true;
        payrollComponentPatchModel.IsBaseForCalculationOvertime = false;
        payrollComponentPatchModel.IsBaseForCalculationDailyWageSupplement = false;
        payrollComponentPatchModel.IsBaseForCalculationDailyWageZw = false;
        payrollComponentPatchModel.BaseForCalculationBter = new KeyModel { Key = 1 };

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);

        var loketContext = GetLoketContext();

        var modelComponent = await loketContext.Set<ModelComponent>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<ModelComponent>(Guid.Parse(componentId)))
            .SingleOrDefaultAsync();
        modelComponent.Should().NotBeNull();
        modelComponent.Description.Should().BeNull();
        modelComponent.Category.Should().BeNull();
        modelComponent.GeneralLedgerAccountNumber.Should().BeNull();
        modelComponent.IsLifeSpanScheme.Should().BeNull();
        modelComponent.Order.Should().BeNull();
        modelComponent.IsVisibleByDefault.Should().BeNull();
    }

    [Theory] // SocialSecurityLiable (nl: SociaalPlichtig) and TaxLiable (nl: BelastingPlichtig)
    [InlineData("********-07e9-0067-0000-************", 1, 1)] // Non-SpecialComponent - ComponentId 103 - Category 30
    [InlineData("********-07e9-015e-0000-************", 4, 2)] // Non-SpecialComponent - ComponentId 350 - Category 11
    [InlineData("********-07e9-01f4-0000-************", 4, null)] // SpecialComponent - ComponentId 500
    public async Task Ok_SocialSecurityLiable_And_TaxLiable(string componentId, int taxLiable, int? socialSecurityLiable)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.TaxLiable = new KeyModel { Key = taxLiable };
        payrollComponentPatchModel.SocialSecurityLiable = socialSecurityLiable is null ? null : new KeyModel { Key = socialSecurityLiable.Value };

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // PaymentDescription (nl: ExcassoOmschrijving)
    [InlineData(null)] // ComponentId 73 - Specific Component Id
    [InlineData("VALUE")] // ComponentId 74 - Specific Component Id
    public async Task Ok_PaymentDescription(string? description)
    {
        // Arrange
        const string componentId = "********-07e9-0104-0000-************"; // ComponentId 260
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.PaymentDescription = description;

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // IsOvertime (nl: OverwerkLoonaangifte)
    [InlineData("********-07e9-0049-0000-************")] // ComponentId 73 - Specific Component Id
    [InlineData("********-07e9-004a-0000-************")] // ComponentId 74 - Specific Component Id
    [InlineData("********-07e9-0fc9-0000-************")] // ComponentId 4041 - Specific Component Id
    [InlineData("********-07e9-0fca-0000-************")] // ComponentId 4042 - Specific Component Id
    [InlineData("********-07e9-0301-0000-************")] // ComponentId 769 - Valid DeductionOrPaymen, TaxLiable, and SocialSecurityLiable
    public async Task Ok_IsOvertime(string componentId)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsOvertime = true;

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // SuppressPrinting (nl: PrintenOnderdrukken)
    public async Task Ok_ApplyPostProcessing_SuppressPrinting()
    {
        // Arrange
        const string componentId = "********-07e9-03bf-0000-************"; // ComponentId 959
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.SuppressPrinting = true; // Set to an invalid value - ApplyPostProcessing will set it to false

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // BalanceSheetSide (nl: Journaal)
    [InlineData("********-07e9-00ef-0000-************")] // ComponentId 239
    [InlineData("********-07e9-01a6-0000-************")] // ComponentId 422
    public async Task Ok_ApplyPostProcessing_BalanceSheetSide(string componentId)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.BalanceSheetSide = new KeyModel { Key = 2 }; // Set to any value - ApplyPostProcessing will set it correctly

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    #endregion

    #region ValidationTestsBadRequestModelValidation

    [Fact]
    public async Task BadRequest_ModelValidation_Null()
    {
        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", Guid.Empty.ToString()),
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Properties_Null()
    {
        // Arrange
        var payrollComponentPatchModel = new PayrollComponentPatchModel();

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", Guid.Empty.ToString()),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Theory]
    [InlineData("")] // empty string
    [InlineData("TEST DESCRIPTION")] // too long, > 14 characters
    public async Task BadRequest_ModelValidation_Description_EmptyOrTooLarge(string description)
    {
        // Arrange
        const string componentId = "********-07e9-0104-0000-************"; // ComponentId 260
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.Description = description;

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory]
    [InlineData("")] // empty string
    [InlineData("INVALID PAYMENT DESCRIPTION")] // too long, > 19 characters
    public async Task BadRequest_ModelValidation_PaymentDescription(string description)
    {
        // Arrange
        const string componentId = "********-07e9-0104-0000-************"; // ComponentId 260
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.PaymentDescription = description; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    #endregion

    #region ValidationTestsBadRequestMessageCode

    [Theory] // IsPayment (nl: Excasso)
    [InlineData("********-07e9-0104-0000-************")] // ComponentId 260
    [InlineData("********-07e9-01df-0000-************")] // ComponentId 479
    public async Task BadRequest_MessageCode_IsPayment(string componentId)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsPayment = false; // Set to an invalid value - ApplyPostProcessing will set it to true

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }


    [Fact] // DeductionOrPayment (nl: BetalingInhouding) for component 260
    public async Task BadRequest_MessageCode_DeductionOrPayment_2_And_CostEmployer_1()
    {
        // Arrange
        const string componentId = "********-07e9-0104-0000-************"; // ComponentId 260
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);

        payrollComponentPatchModel.DeductionOrPayment = new KeyModel { Key = 2 };

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_MessageCode_AllCtProperties_Invalid()
    {
        // Arrange
        const string componentId = "********-07e9-0163-0000-************"; // ComponentId 355
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);

        // Set invalid values for code table properties
        payrollComponentPatchModel.DeductionOrPayment = new KeyModel { Key = 3 };
        payrollComponentPatchModel.PaymentPeriod = new KeyModel { Key = 8 };
        payrollComponentPatchModel.TaxLiable = new KeyModel { Key = 13 };
        payrollComponentPatchModel.SocialSecurityLiable = new KeyModel { Key = 3 };
        payrollComponentPatchModel.HoursIndication = new KeyModel { Key = 3 };
        payrollComponentPatchModel.CostsEmployer = new KeyModel { Key = 2 };
        payrollComponentPatchModel.BaseForCalculationBter = new KeyModel { Key = 3 };

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 0 → Column (nl: Kolom)
    public async Task BadRequest_MessageCode_Column_1()
    {
        // Arrange
        const string componentId = "********-07e9-016c-0000-************"; // ComponentId 364 with Column = 0
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        // Set to invalid values
        payrollComponentPatchModel.SocialSecurityLiable = new KeyModel { Key = 1 };

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 1 → DeductionOrPayment (nl: BetalingInhouding)
    [InlineData("********-07e9-0046-0000-************", null)]  // ComponentId 70 - Non-SpecialComponent & Non-Category (11 or 30)
    [InlineData("********-07e9-00cc-0000-************", 1)] // Component 204 - component with removed post-processing logic.
    [InlineData("********-07e9-0101-0000-************", 1)] // Component 257 - component with removed post-processing logic.
    [InlineData("********-07e9-0109-0000-************", 1)] // Component 265 - component with removed post-processing logic.
    public async Task BadRequest_MessageCode_DeductionOrPayment_1(string componentId, int? deductionOrPaymentKey)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.DeductionOrPayment =
            deductionOrPaymentKey is null ? null : new KeyModel { Key = deductionOrPaymentKey }; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 2 → DeductionOrPayment (nl: BetalingInhouding)
    public async Task BadRequest_MessageCode_DeductionOrPayment_2()
    {
        // Arrange
        const string componentId = "********-07e9-0066-0000-************"; // Component 102 with PayrollPeriods
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_PA_2025, componentId);
        payrollComponentPatchModel.DeductionOrPayment = null; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 3 → TaxLiable (nl: BelastingPlichtig)
    [InlineData("********-07e9-029e-0000-************", 2)] // ComponentId 670 - Non-SpecialComponent - Non-Category (11 or 30)
    [InlineData("********-07e9-00cc-0000-************", 1)] // Component 204 - component with removed post-processing logic.
    [InlineData("********-07e9-0101-0000-************", 1)] // Component 257 - component with removed post-processing logic.
    [InlineData("********-07e9-0109-0000-************", 1)] // Component 265 - component with removed post-processing logic.
    public async Task BadRequest_MessageCode_TaxLiable_1(string componentId, int? taxLiableKey)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.TaxLiable = taxLiableKey is null ? null : new KeyModel { Key = taxLiableKey }; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 4 → SocialSecurityLiable (nl: SociaalPlichtig)
    [InlineData("********-07e9-04be-0000-************", null)] // ComponentId 1214 - Non-SpecialComponent - Non-Category (11 or 30)
    [InlineData("********-07e9-00cc-0000-************", 1)] // Component 204 - component with removed post-processing logic.
    [InlineData("********-07e9-0101-0000-************", 1)] // Component 257 - component with removed post-processing logic.
    [InlineData("********-07e9-0109-0000-************", 1)] // Component 265 - component with removed post-processing logic.
    public async Task BadRequest_MessageCode_SocialSecurityLiable_1(string componentId, int? socialSecurityLiableKey)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.SocialSecurityLiable = socialSecurityLiableKey is null ? null : new KeyModel() { Key = socialSecurityLiableKey }; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 5 → PaymentPeriod (nl: BetalingsPeriode)
    [InlineData(5, "********-07e9-0064-0000-************")] // ComponentId 100 (<101) - PaymentPeriod 5
    [InlineData(6, "********-07e9-0064-0000-************")] // ComponentId 100 (>101) - PaymentPeriod 6
    [InlineData(5, "********-07e9-0079-0000-************")] // ComponentId 121 (>120) - PaymentPeriod 5
    [InlineData(6, "********-07e9-0079-0000-************")] // ComponentId 121 (>120) - PaymentPeriod 6
    public async Task BadRequest_MessageCode_PaymentPeriod_1(int paymentPeriod, string componentId)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.PaymentPeriod = new KeyModel { Key = paymentPeriod }; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 6 → IsPayment (nl: Excasso)
    public async Task BadRequest_MessageCode_IsPayment_1()
    {
        // Arrange
        const string componentId = "********-07e9-02a1-0000-************"; // ComponentId 499
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsPayment = true; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 7 → IsPayment (nl: Excasso)
    [InlineData("********-07e9-0104-0000-************")] // ComponentId 260
    [InlineData("********-07e9-01df-0000-************")] // ComponentId 479
    public async Task BadRequest_MessageCode_IsPayment_2(string componentId)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsPayment = false; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 8 → PaymentDescription (nl: ExcassoOmschrijving)
    public async Task BadRequest_MessageCode_PaymentDescription_1()
    {
        // Arrange
        const string componentId = "********-07e9-0105-0000-************"; // ComponentId 261
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.PaymentDescription = "Value"; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 9 → CostsEmployer (nl: KostenWerkgever)
    [InlineData(1)]
    [InlineData(-1)]
    public async Task BadRequest_MessageCode_CostsEmployer_1(int costsEmployer)
    {
        // Arrange
        const string componentId = "********-07e9-01ef-0000-************"; // ComponentId 495
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.CostsEmployer = new KeyModel { Key = costsEmployer }; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 10 → CostsEmployer (nl: KostenWerkgever)
    public async Task BadRequest_MessageCode_CostsEmployer_2()
    {
        // Arrange
        const string componentId = "********-07e9-0052-0000-************"; // ComponentId 82 - DeductionOrPayment = 1
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.CostsEmployer = new KeyModel { Key = -1 }; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 11 → CostsEmployer (nl: KostenWerkgever)
    public async Task BadRequest_MessageCode_CostsEmployer_3()
    {
        // Arrange
        const string componentId = "********-07e9-01a4-0000-************"; // ComponentId 420 - DeductionOrPayment = 2
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.CostsEmployer = new KeyModel { Key = 1 }; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 12 → IsNetToGross (nl: NettoBruto)
    [InlineData("********-07e9-0068-0000-************")] // ComponentId 104 - DeductionOrPayment = 0 - TaxLiable = 1 - Category = 30 - Invalid DeductionOrPayment
    [InlineData("********-07e9-006e-0000-************")] // ComponentId 110 - DeductionOrPayment = 0 - TaxLiable = 3 - Category = 30 - Invalid DeductionOrPayment
    [InlineData("********-07e9-0065-0000-************")] // ComponentId 101 - DeductionOrPayment = 1 - TaxLiable = 0 - Category = 30 - Invalid TaxLiable
    [InlineData("********-07e9-02fa-0000-************")] // ComponentId 762 - DeductionOrPayment = 1 - TaxLiable = 1 - Category = 98 - Invalid Category
    public async Task BadRequest_MessageCode_IsNetToGross_1(string componentId)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsNetToGross = true; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 13 → IsNetToGross (nl: NettoBruto)
    public async Task BadRequest_MessageCode_IsNetToGross_2()
    {
        // Arrange
        const string componentId = "********-07e9-0121-0000-************"; // ComponentId 289 - IsBaseForCalculationOvertime (nl: GrondslagOverwerk) = true
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsNetToGross = true; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 14 → IsNetToGross (nl: NettoBruto)
    public async Task BadRequest_MessageCode_IsNetToGross_3()
    {
        // Arrange
        const string componentId = "********-07e9-011f-0000-************"; // ComponentId 287 - IsBaseForCalculationDailyWageSupplement (nl: GrondslagDagloonSuppletie) = true
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsNetToGross = true; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 15 → IsNetToGross (nl: NettoBruto)
    public async Task BadRequest_MessageCode_IsNetToGross_4()
    {
        // Arrange
        const string componentId = "********-07e9-011e-0000-************"; // ComponentId 286 - IsBaseForCalculationDailyWageZw (nl: GrondslagDagloonZw) = true
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsNetToGross = true; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 16 → IsNetToGross (nl: NettoBruto)
    public async Task BadRequest_MessageCode_IsNetToGross_5()
    {
        // Arrange
        const string componentId = "********-07e9-013a-0000-************"; // ComponentId 314 - BaseForCalculationBter (nl: GrondslagBterLoon) = 2
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsNetToGross = true; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 17 → IsOvertime (nl: OverwerkLoonaangifte)
    [InlineData("********-07e9-0049-0000-************")] // ComponentId 73
    [InlineData("********-07e9-004a-0000-************")] // ComponentId 74
    [InlineData("********-07e9-0fc9-0000-************")] // ComponentId 4041
    [InlineData("********-07e9-0fca-0000-************")] // ComponentId 4042
    public async Task BadRequest_MessageCode_IsOvertime_1(string componentId)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsOvertime = false; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory] // 18 → IsOvertime (nl: OverwerkLoonaangifte)
    [InlineData("********-07e9-0329-0000-************")] // ComponentId 809 - DeductionOrPayment = 0 - TaxLiable = 1 - SocialSecurityLiable = 1
    [InlineData("********-07e9-010f-0000-************")] // ComponentId 271 - DeductionOrPayment = 0 - TaxLiable = 3 - SocialSecurityLiable = 1
    [InlineData("********-07e9-032c-0000-************")] // ComponentId 812 - DeductionOrPayment = 1 - TaxLiable = 0
    [InlineData("********-07e9-0138-0000-************")] // ComponentId 312 - DeductionOrPayment = 2 - TaxLiable = 2
    [InlineData("********-07e9-0689-0000-************")] // ComponentId 1673 - DeductionOrPayment = 1 - TaxLiable = 1 - SocialSecurityLiable = 0
    [InlineData("********-07e9-01e6-0000-************")] // ComponentId 486 - DeductionOrPayment = 1 - TaxLiable = 3 - SocialSecurityLiable = 0
    public async Task BadRequest_MessageCode_IsOvertime_2(string componentId)
    {
        // Arrange
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);
        payrollComponentPatchModel.IsOvertime = true; // Set to an invalid value

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact] // 22-Warning → SocialSecurityLiable (nl: SociaalPlichtig)
    public async Task Ok_Warning_SocialSecurityLiable()
    {
        // Arrange
        const string componentId = "********-07e9-01e6-0000-************"; // ComponentId 486 
        var payrollComponentPatchModel = await GetPayrollComponentPatchModelAsync(QA_PayrollComponent_PATCH_CLA_2025, componentId);

        // Act
        var patchResult = await CallApiAsync(
            HttpMethod.Patch,
            $"{PayrollComponentRoutes.PatchPayrollComponentByPayrollComponentIdAsync}".Replace("{payrollComponentId:guid}", componentId),
            payrollComponentPatchModel,
            HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    #endregion

    private async Task<PayrollComponentPatchModel> GetPayrollComponentPatchModelAsync(
        string yearId,
        string componentId)
    {
        var getResult = await CallApiAsync(
            HttpMethod.Get,
            $"{PayrollComponentRoutes.GetPayrollComponentsByYearIdAsync}".Replace("{yearId:guid}", yearId) + $"?filter=id eq '{componentId}'",
            HttpStatusCode.OK);

        var getObject = JsonConvert.DeserializeObject<ListResult<PayrollComponentModel>>(getResult!)
                        ?? new ListResult<PayrollComponentModel>();

        return this.Mapper.Map<PayrollComponentPatchModel>(getObject.Collection!.FirstOrDefault()!);
    }
}