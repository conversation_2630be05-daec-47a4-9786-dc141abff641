using System.Text.Encodings.Web;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.PayrollAdministration.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollAdministration;

[Collection(EntityNames.PayrollAdministration)]
public class GetPayrollAdministrationsByBearerTokenTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollAdministration";
    protected override bool UseTransaction => false;

    private const string OrderByName = "orderBy=name";
    private const string FilterByName = $"filter=name lk 'QA_PayrollConfiguration'";
    private readonly string filterCompanyName = UrlEncoder.Default.Encode(" and employer.companyName eq 'QA_PayrollConfiguration1'");

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_OrderByName()
    {
        // Act
        var getUri =
            $"{PayrollAdministrationRoutes.GetPayrollAdministrationsByBearerTokenAsync}?{FilterByName}&{OrderByName}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_FilterByCompanyName()
    {
        // Act
        var getUri =
            $"{PayrollAdministrationRoutes.GetPayrollAdministrationsByBearerTokenAsync}?{FilterByName}{this.filterCompanyName}&{OrderByName}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    [Claims(ClientName = "Loket3", Omgeving = 11, UserId = "5ad6ed4e-e059-4c21-a77f-04955458c181", Rol = RolEnum.Provider)] // QA_PayrollConfiguration2
    public async Task Ok_QA_PayrollConfiguration2_OrderByName()
    {
        // Act
        var getUri =
            $"{PayrollAdministrationRoutes.GetPayrollAdministrationsByBearerTokenAsync}?{FilterByName}&{OrderByName}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_X_ReportInput_header() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{PayrollAdministrationRoutes.GetPayrollAdministrationsByBearerTokenAsync}?{FilterByName}&{OrderByName}",
                Method = HttpMethod.Get,
                Headers = new Dictionary<string, string>
                {
                    { "accept", "text/csv" },
                    { "content-type", "application/json" },
                    { "x-reportinput", "{\"FileNameWithoutExtension\":\"PayrollAdministrations\",\"Fields\":[{\"FieldName\":\"id\",\"ReportColumnName\":\"Id\"},{\"FieldName\":\"name\",\"ReportColumnName\":\"Name\"},{\"FieldName\":\"groupCode\",\"ReportColumnName\":\"GroupCode\"},{\"FieldName\":\"employer.companyName\",\"ReportColumnName\":\"Employer\"},{\"FieldName\":\"collectiveLaborAgreement.description\",\"ReportColumnName\":\"CollectiveLaborAgreement\"}]}" }
                },
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                HeaderValidations = [
                    new HeaderValidation
                    {
                        Key = "Content-Type",
                        Value = "text/csv; charset=utf-8",
                        Mode = ValidationMode.Strict,
                    },
                    new HeaderValidation // Note: Not really needed but for testing the framework
                    {
                        Key = "Content-Type",
                        Value = "text/csv;",
                        Mode = ValidationMode.Partial,
                    },
                    new HeaderValidation
                    {
                        Key = "Content-Disposition",
                        Value = @"attachment; filename=""PayrollAdministrations .* ([0-1]\d|2[0-3]):[0-5]\d:[0-5]\d\.csv""",
                        Mode = ValidationMode.Pattern,
                    },
                ],
                BodyValidation = new BodyValidation(),
            }
        );

    // TODO GlobalFilter tests
}