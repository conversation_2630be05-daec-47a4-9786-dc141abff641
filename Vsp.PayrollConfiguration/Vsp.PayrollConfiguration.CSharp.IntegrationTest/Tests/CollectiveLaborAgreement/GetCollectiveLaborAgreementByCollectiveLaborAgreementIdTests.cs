using Vsp.PayrollConfiguration.CollectiveLaborAgreement.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.CollectiveLaborAgreement;

[Collection(EntityNames.CollectiveLaborAgreement)]
public class GetCollectiveLaborAgreementByCollectiveLaborAgreementIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "CollectiveLaborAgreement";
    protected override bool UseTransaction => false;

    private const string QA_PayrollConfiguration_CLA_ForLevel_CLA = "c59445b9-6c03-4423-b405-94e89f250895";
    private const string QA_PayrollConfiguration1_CLA_ForLevel_WM = "8c87ffab-20e3-4bcd-808c-8eb9f4e8dfdf";
    private const string QA_PayrollConfiguration1_CLA_ForLevel_PA = "dfdaa0a5-bce8-4ddd-8e1c-42554334c342";

    [Fact]
    public async Task Ok_QA_PayrollConfiguration_CLA_ForLevel_CLA()
    {
        // Act
        var getUri = CollectiveLaborAgreementRoutes.GetCollectiveLaborAgreementByCollectiveLaborAgreementIdAsync.Replace("{collectiveLaborAgreementId:guid}", QA_PayrollConfiguration_CLA_ForLevel_CLA);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_CLA_ForLevel_WM()
    {
        // Act
        var getUri = CollectiveLaborAgreementRoutes.GetCollectiveLaborAgreementByCollectiveLaborAgreementIdAsync.Replace("{collectiveLaborAgreementId:guid}", QA_PayrollConfiguration1_CLA_ForLevel_WM);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_CLA_ForLevel_PA()
    {
        // Act
        var getUri = CollectiveLaborAgreementRoutes.GetCollectiveLaborAgreementByCollectiveLaborAgreementIdAsync.Replace("{collectiveLaborAgreementId:guid}", QA_PayrollConfiguration1_CLA_ForLevel_PA);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }
}