using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.Shift.Models;
using Vsp.PayrollConfiguration.Shift.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Shift;

[Collection(EntityNames.Shift)]
public class PatchShiftByShiftIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Shift";
    protected override bool UseTransaction => true;

    public static readonly Guid QA_Shift_PATCH_CLA_ShiftId = Guid.Parse("0000094f-07e9-0001-0100-000000000000");
    public static readonly Guid QA_Shift_PATCH_WM_ShiftId = Guid.Parse("00000950-07e9-0001-0100-000000000000");
    public static readonly Guid QA_Shift_PATCH_PA_ShiftId = Guid.Parse("00000951-07e9-0002-0100-000000000000");

    public static readonly Guid QA_Shift_PATCH_PA_ShiftIn_PA_ToDeleteDueToPatch = Guid.Parse("00000951-07e9-0001-0100-000000000000");

    [Fact]
    public async Task Ok_QA_Shift_PATCH_CLA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", QA_Shift_PATCH_CLA_ShiftId.ToString()),
                Method = HttpMethod.Patch,
                Body = new ShiftPatchModel { FullTimeHoursPerWeek = 14.67M, BonusPercentage = 17.89M },
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_QA_Shift_PATCH_WM()
    {
        // Act
        var patchUri = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", QA_Shift_PATCH_WM_ShiftId.ToString());
        var patchModel = new ShiftPatchModel { FullTimeHoursPerWeek = 15.31M, BonusPercentage = 19.63M };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task Ok_QA_Shift_PATCH_PA()
    {
        // Act
        var patchUri = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", QA_Shift_PATCH_PA_ShiftId.ToString());
        var patchModel = new ShiftPatchModel { FullTimeHoursPerWeek = 18.92M, BonusPercentage = 24.83M };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task Ok_QA_Shift_PATCH_PA_CausePALevelDeletion()
    {
        // Act
        var patchUri = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", QA_Shift_PATCH_PA_ShiftIn_PA_ToDeleteDueToPatch.ToString());
        var patchModel = new ShiftPatchModel { FullTimeHoursPerWeek = 40M, BonusPercentage = 0M };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task Ok_QA_Shift_PATCH_PA_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", QA_Shift_PATCH_PA_ShiftId.ToString()),
                Method = HttpMethod.Patch,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = new ShiftPatchModel { FullTimeHoursPerWeek = 99M, BonusPercentage = 99M }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shift was not updated
        using var loketContext = GetLoketContext();
        var shift = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Shift>(QA_Shift_PATCH_PA_ShiftId))
            .SingleOrDefaultAsync();
        shift.Should().NotBeNull();
        shift.FullTimeHoursPerWeek.Should().NotBe(99M);
        shift.BonusPercentage.Should().NotBe(99M);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Null()
    {
        // Act
        var patchUri = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", Guid.Empty.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Properties_Null()
    {
        // Act
        var patchUri = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", Guid.Empty.ToString());
        var patchModel = new ShiftPatchModel();
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(100)]
    public async Task BadRequest_ModelValidation_FullTimeHoursPerWeek_OutOfRange(decimal fullTimeHoursPerWeek)
    {
        // Act
        var patchUri = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", Guid.Empty.ToString());
        var patchModel = new ShiftPatchModel { FullTimeHoursPerWeek = fullTimeHoursPerWeek, BonusPercentage = 24.83M };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_FullTimeHoursPerWeek_MoreThanTwoDecimals()
    {
        // Act
        var patchUri =
            $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", Guid.Empty.ToString());
        var patchModel = """
                         {
                             "fullTimeHoursPerWeek": 0.123,
                             "bonusPercentage": 9.8
                         }
                         """;
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Theory]
    [InlineData(-0.001)]
    [InlineData(100.001)]
    public async Task BadRequest_ModelValidation_BonusPercentage_OutOfRange(decimal bonusPercentage)
    {
        // Act
        var patchUri = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", Guid.Empty.ToString());
        var patchModel = new ShiftPatchModel { FullTimeHoursPerWeek = 3, BonusPercentage = bonusPercentage };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_BonusPercentage_MoreThanThreeDecimals()
    {
        // Act
        var patchUri =
            $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", Guid.Empty.ToString());
        var patchModel = """
                         {
                             "fullTimeHoursPerWeek": 0.12,
                             "bonusPercentage": 9.8234
                         }
                         """;
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task Forbidden_NotFound()
    {
        // Arrange
        var patchUri = $"{ShiftRoutes.PatchShiftByShiftIdAsync}".Replace("{shiftId:guid}", Guid.Empty.ToString());
        var patchModel = new ShiftPatchModel { FullTimeHoursPerWeek = 14.67M, BonusPercentage = 17.89M };

        // Act
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.Forbidden);

        // Assert
        await VerifyJsonAsync(patchResult, ignoredByNameMembers: ["stackTrace", "errorCode"]);
    }
}