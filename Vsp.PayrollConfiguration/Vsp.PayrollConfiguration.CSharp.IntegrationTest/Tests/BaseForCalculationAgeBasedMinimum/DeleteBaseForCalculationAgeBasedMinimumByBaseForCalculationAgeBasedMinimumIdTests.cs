using Vsp.PayrollConfiguration.BaseForCalculationAgeBasedMinimum.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationAgeBasedMinimum;

[Collection(EntityNames.BaseForCalculationAgeBasedMinimum)]
public class DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationAgeBasedMinimum";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_BFC_AgeBasedMinimum_DELETE_CLA_BFC1 = Guid.Parse("000009e2-07e9-0001-0100-000000000000"); // // QA_BFC_AgeBasedMinimum_DELETE_CLA_ForLevel_CLA BFC1 year 2025
    private static readonly Guid QA_BFC_AgeBasedMinimum_DELETE_WM_BFC1 = Guid.Parse("000009e4-07e9-0001-0100-000000000000"); // QA_BFC_AgeBasedMinimum_DELETE_WM_ForLevel_WM BFC1 year 2025
    private static readonly Guid QA_BFC_AgeBasedMinimum_DELETE_PA_BFC1 = Guid.Parse("000009e1-07e9-0001-0100-000000000000"); // QA_BFC_AgeBasedMinimum_DELETE_PA_BFC1 year 2025

    private static readonly Guid QA_BFC_AgeBasedMinimum_DELETE_CLA_Deletable_NoChildren = Guid.Parse("000009e2-07e9-0001-2d00-0c0000000000"); // Deletable BaseForCalculation_AgeBasedMinimum for CLA with no children - Year 2025
    private static readonly Guid QA_BFC_AgeBasedMinimum_DELETE_WM_Deletable_NoChildren = Guid.Parse("000009e4-07e9-0001-2300-010000000000"); // Deletable BaseForCalculation_AgeBasedMinimum for WM with no children - Year 2025
    private static readonly Guid QA_BFC_AgeBasedMinimum_DELETE_PA_Deletable_NoChildren = Guid.Parse("000009e1-07e9-0001-6300-030000000000"); // Deletable BaseForCalculation_AgeBasedMinimum for PA with no children - Year 2025

    private static readonly Guid QA_BFC_AgeBasedMinimum_DELETE_CLA_EntityHasChildren = Guid.Parse("000009df-07e9-0001-0f00-010000000000"); // Non-deletable BaseForCalculation_AgeBasedMinimum for CLA due to dependent child inheritance - Year 2025
    private static readonly Guid QA_BFC_AgeBasedMinimum_DELETE_PA_PayrollPeriod_FirstPeriodCannotBeDeleted = Guid.Parse("000009e1-07e9-0001-6300-010000000000"); // Non-deletable BaseForCalculation_AgeBasedMinimum for PA as it is the first period with data for later periods - Year 2025

    #region OK Tests

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_DELETE_CLA_2025_Deletable_NoChildren()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_DELETE_CLA_Deletable_NoChildren.ToString()),
                Method = HttpMethod.Delete
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

        var getUri =
            $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_DELETE_CLA_BFC1.ToString()) + 
            $"?filter=id eq '{QA_BFC_AgeBasedMinimum_DELETE_CLA_Deletable_NoChildren}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<BaseForCalculationAgeBasedMinimumModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_DELETE_WM_2025_Deletable_NoChildren()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_DELETE_WM_Deletable_NoChildren.ToString()),
                Method = HttpMethod.Delete
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

        var getUri =
            $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_DELETE_WM_BFC1.ToString()) + 
            $"?filter=id eq '{QA_BFC_AgeBasedMinimum_DELETE_WM_Deletable_NoChildren}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<BaseForCalculationAgeBasedMinimumModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_DELETE_PA_2025_Deletable_NoChildren()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_DELETE_PA_Deletable_NoChildren.ToString()),
                Method = HttpMethod.Delete
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

        var getUri =
            $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_DELETE_PA_BFC1.ToString()) + 
            $"?filter=id eq '{QA_BFC_AgeBasedMinimum_DELETE_PA_Deletable_NoChildren}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<BaseForCalculationAgeBasedMinimumModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    #endregion

    #region BadRequest Tests - Inheritance Validator Errors

    [Fact]
    public async Task BadRequest_MessageCode_EntityHasChildren() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_DELETE_CLA_EntityHasChildren.ToString()),
                Method = HttpMethod.Delete
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodCannotBeDeleted() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_DELETE_PA_PayrollPeriod_FirstPeriodCannotBeDeleted.ToString()),
                Method = HttpMethod.Delete
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = new JsonBodyValidation()
            }
        );

    #endregion
}