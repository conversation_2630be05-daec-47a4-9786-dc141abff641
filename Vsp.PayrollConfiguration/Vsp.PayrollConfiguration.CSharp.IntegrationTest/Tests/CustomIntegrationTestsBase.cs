namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests;

[Collection("General")]
[Claims(ClientName = "Loket3", Omgeving = 11, UserId = "a4ed6fe8-9ec1-42a7-92a2-a44534d57344", Rol = RolEnum.Provider)] // QA_PayrollConfiguration1
public abstract class CustomIntegrationTestsBase(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : IntegrationTestsBase<Program, ILoketContext, LoketContext>(fixture)
{
    protected override string SnapshotsFolder
    {
        get
        {
            var currentDirectory = Environment.CurrentDirectory;
            var relativePath = $"{currentDirectory}/../../../Snapshots";
            var fullPath = Path.GetFullPath(relativePath);
            return fullPath;
        }
    }

    protected IMapper Mapper { get; } = null!;

    protected CustomIntegrationTestsBase(
        WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture,
        List<Profile> profiles)
        : this(fixture)
    {
        var config = new MapperConfiguration(cfg =>
        {
            foreach (var profile in profiles) cfg.AddProfile(profile);
        });

        this.Mapper = config.CreateMapper();
    }
}