using System.Text.Encodings.Web;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.WageModel.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.WageModel;

[Collection(EntityNames.WageModel)]
public class GetWageModelsByBearerTokenTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "WageModel";
    protected override bool UseTransaction => false;

    private const string FilterByDescription = "filter=description lk 'QA_PayrollConfiguration'";
    private const string OrderByDescription = "orderBy=description";
    private const string OrderByCLA = "orderBy=-collectiveLaborAgreement.id";

    private readonly string stricterFilter = $"filter={UrlEncoder.Default.Encode("description eq 'QA_PayrollConfiguration1_WM_ForLevel_PA'")}";

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1()
    {
        // Act
        var getUri = $"{WageModelRoutes.GetWageModelsByBearerTokenAsync}?{FilterByDescription}&{OrderByDescription}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    [Claims(ClientName = "Loket3", Omgeving = 11, UserId = "5ad6ed4e-e059-4c21-a77f-04955458c181", Rol = RolEnum.Provider)] // QA_PayrollConfiguration2
    public async Task Ok_QA_PayrollConfiguration2()
    {
        // Act
        var getUri =
            $"{WageModelRoutes.GetWageModelsByBearerTokenAsync}?{FilterByDescription}&{OrderByDescription}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_StricterFilter()
    {
        // Act
        var getUri =
            $"{WageModelRoutes.GetWageModelsByBearerTokenAsync}?{this.stricterFilter}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_OrderByCLA()
    {
        // Act
        var getUri =
            $"{WageModelRoutes.GetWageModelsByBearerTokenAsync}?{FilterByDescription}&{OrderByCLA}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_X_ReportInput_header() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{WageModelRoutes.GetWageModelsByBearerTokenAsync}?{FilterByDescription}&{OrderByDescription}",
                Method = HttpMethod.Get,
                Headers = new Dictionary<string, string>
                {
                    { "accept", "text/csv" },
                    { "content-type", "application/json" },
                    { "x-reportinput", "{\"FileNameWithoutExtension\":\"WageModels\",\"Fields\":[{\"FieldName\":\"id\",\"ReportColumnName\":\"Id\"},{\"FieldName\":\"description\",\"ReportColumnName\":\"Description\"},{\"FieldName\":\"comment\",\"ReportColumnName\":\"Comment\"},{\"FieldName\":\"collectiveLaborAgreement.description\",\"ReportColumnName\":\"CollectiveLaborAgreement\"}]}" }
                },
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                HeaderValidations = [
                    new HeaderValidation
                    {
                        Key = "Content-Type",
                        Value = "text/csv; charset=utf-8",
                        Mode = ValidationMode.Strict,
                    },
                    new HeaderValidation
                    {
                        Key = "Content-Disposition",
                        Value = @"attachment; filename=""WageModels .* ([0-1]\d|2[0-3]):[0-5]\d:[0-5]\d\.csv""",
                        Mode = ValidationMode.Pattern,
                    },
                ],
                BodyValidation = new BodyValidation(),
            }
        );
}