using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.DifferentiatedReturnToWorkFund.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.DifferentiatedReturnToWorkFund;

[Collection(EntityNames.DifferentiatedReturnToWorkFund)]
public class GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "DifferentiatedReturnToWorkFund";
    protected override bool UseTransaction => false;

    private static readonly Guid QA_DifferentiatedReturnToWorkFund_GET_PA_2025 = Guid.Parse("00000923-07e9-0000-0000-000000000000");
    private static readonly Guid QA_DifferentiatedReturnToWorkFund = Guid.Parse("55d93032-b51d-4dc7-94aa-b20115f8ed01");

    private const string OrderBy = "orderBy=startPayrollperiod.periodNumber";

    [Fact]
    public async Task Ok_QA_DifferentiatedReturnToWorkFund_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{DifferentiatedReturnToWorkFundRoutes.GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearIdAsync}"
                .Replace("{yearId:guid}", QA_DifferentiatedReturnToWorkFund_GET_PA_2025.ToString())
            + $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_QA_DifferentiatedReturnToWorkFund_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{DifferentiatedReturnToWorkFundRoutes.GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearIdAsync}"
                .Replace("{yearId:guid}", QA_DifferentiatedReturnToWorkFund_GET_PA_2025.ToString())
            + $"?{OrderBy}"
            + $"&filter=id eq '{QA_DifferentiatedReturnToWorkFund}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}