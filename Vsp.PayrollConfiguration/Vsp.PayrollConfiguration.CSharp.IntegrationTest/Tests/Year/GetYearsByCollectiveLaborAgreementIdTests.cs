using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Year.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Year;

[Collection(EntityNames.Year)]
public class GetYearsByCollectiveLaborAgreementIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Year";
    protected override bool UseTransaction => false;

    public static readonly Guid QA_Year_GET_CLA = Guid.Parse("6a1d3e98-8b9f-493f-b9b6-2c52655f49d1");

    private const string OrderBy = "orderBy=year";
    private const string OrderByDesc = "orderBy=-year";

    private const string FilterYearId = "0000093b-07e8-0000-0000-000000000000"; // 2024
    private const string FilterUnexistentYearId = "0000093b-07e3-0000-0000-000000000000"; // 2019


    [Fact]
    public async Task Ok_OrderBy_Year()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByCollectionLaborAgreementIdAsync}?{OrderBy}".Replace("{collectiveLaborAgreementId:guid}", QA_Year_GET_CLA.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_OrderBy_Year_Desc()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByCollectionLaborAgreementIdAsync}?{OrderByDesc}".Replace("{collectiveLaborAgreementId:guid}", QA_Year_GET_CLA.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_FilterBy_Id()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByCollectionLaborAgreementIdAsync}?{OrderBy}&filter=id eq '{FilterYearId}'".Replace("{collectiveLaborAgreementId:guid}", QA_Year_GET_CLA.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_Empty()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByCollectionLaborAgreementIdAsync}?{OrderBy}&filter=id eq '{FilterUnexistentYearId}'".Replace("{collectiveLaborAgreementId:guid}", QA_Year_GET_CLA.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }
}