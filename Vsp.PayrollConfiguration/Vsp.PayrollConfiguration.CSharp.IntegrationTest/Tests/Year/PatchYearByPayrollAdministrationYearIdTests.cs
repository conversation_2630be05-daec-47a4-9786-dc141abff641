using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Domain.Year.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Models;
using Vsp.PayrollConfiguration.Repository.Entities;
using Vsp.PayrollConfiguration.Repository.Entities.Base;
using Vsp.PayrollConfiguration.Year.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Year;

[Collection(EntityNames.Year)]
public class PatchYearByPayrollAdministrationYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Year";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_Year_PATCH_PA_2020 = Guid.Parse("00000947-07e4-0000-0000-000000000000"); // 2020 at PA level
    private static readonly Guid QA_Year_PATCH_PA_2022 = Guid.Parse("00000947-07e6-0000-0000-000000000000"); // 2022 at PA level
    private static readonly Guid QA_Year_PATCH_PA_2024 = Guid.Parse("00000947-07e8-0000-0000-000000000000"); // 2024 at PA level
    private static readonly Guid QA_Year_PATCH_PA_2025 = Guid.Parse("00000947-07e9-0000-0000-000000000000"); // 2025 at PA level

    [Fact]
    public async Task Ok_Valid_InheritanceProperties()
    {
        // Arrange
        var patchModel = new YearPaPatchModel
        {
            StandardShift = new StandardShiftPatchModel { ShiftNumber = 2 },
            StandardEmployeeProfile = new StandardEmployeeProfilePatchModel { EmployeeProfileNumber = 1 },
            TestYear = true,
            ZwSelfInsurerStartPayrollPeriod = null,
            Aof = null,
            DateAvailableEss = new DateOnly(2025, 4, 30), // do not modify database value
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2020.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_Valid_SpecificProperties()
    {
        // Arrange
        var patchModel = new YearPaPatchModel
        {
            StandardShift = new StandardShiftPatchModel { ShiftNumber = 1 },
            StandardEmployeeProfile = null,
            TestYear = true,
            ZwSelfInsurerStartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 },
            Aof = new KeyModel { Key = 2 },
            DateAvailableEss = new DateOnly(2025, 4, 30), // do not modify database value
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2022.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_EmptyOverride_EntityNotDeleted()
    {
        // Arrange
        var loketContext = GetLoketContext();
        var existingModelEntity = await loketContext.Set<ModelYear>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<ModelYear>(QA_Year_PATCH_PA_2020))
            .SingleOrDefaultAsync();
        existingModelEntity.Should().NotBeNull();
        existingModelEntity.StandardShiftNumber.Should().NotBeNull();
        existingModelEntity.StandardEmployeeProfileNumber.Should().NotBeNull();

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2020.ToString());
        var patchModel = new YearPaPatchModel()
        {
            StandardShift = null, // WM value
            StandardEmployeeProfile = new() { EmployeeProfileNumber = 1 }, // WM value
            TestYear = true,
            ZwSelfInsurerStartPayrollPeriod = new PayrollPeriodNumberModel() { PeriodNumber = 1 },
            Aof = null,
            DateAvailableEss = new DateOnly(2025, 4, 30), // do not modify database value,
            SendEssMail = false,
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);

        var updatedModelEntity = await loketContext.Set<ModelYear>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<ModelYear>(QA_Year_PATCH_PA_2020))
            .SingleOrDefaultAsync();
        updatedModelEntity.Should().NotBeNull();
        updatedModelEntity.StandardShiftNumber.Should().BeNull();
        updatedModelEntity.StandardEmployeeProfileNumber.Should().BeNull();
    }

    [Fact]
    public async Task Ok_For_DateAvailableEss()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            DateAvailableEss = new DateOnly(2098, 1, 1),
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2024.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task OK_DateAvailableEss_AllowsNullValue()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            DateAvailableEss = null,
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2025.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_For_SendEssMail()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            SendEssMail = true,
            DateAvailableEss = new DateOnly(2099, 5, 6), // do not modify database value
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2024.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Null()
    {
        // Arrange
        YearPaPatchModel? payload = null;

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2020.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_IllegalNullValues()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = null,
            ZwSelfInsurerStartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = null },
            Aof = new KeyModel { Key = null },
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2020.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_DateAvailableEss_IsRequired()
    {
        // Arrange
        var payload = @"{
            testYear: false,
            aof:  { key: 1 },
        }";

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2025.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_SendEssMail_IsRequired()
    {
        // Arrange
        var payload = @"{
            testYear: false,
            aof:  { key: 1 },
            dateAvailableEss: null
        }";

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2025.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_SendEssMail_CannotBeNull()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            DateAvailableEss = null,
            SendEssMail = null,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2025.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    // Make sure the WM property validations are triggered
    [Fact]
    public async Task BadRequest_NonValid_InheritanceProperties()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            StandardShift = new StandardShiftPatchModel { ShiftNumber = 3 },
            StandardEmployeeProfile = new StandardEmployeeProfilePatchModel { EmployeeProfileNumber = 3 },
            TestYear = true,
            ZwSelfInsurerStartPayrollPeriod = null,
            Aof = null,
            SendEssMail = false,
            DateAvailableEss = new DateOnly(2025, 4, 30),
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2020.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_TestYear_PreviousYearNotTest"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_TestYear_PreviousYearNotTest()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = true,
            Aof = new KeyModel { Key = 1 },
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2025.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_TestYear_PayrollTaxReturnPerformed"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_TestYear_PayrollTaxReturnPerformed()
    {
        // Arrange
        var yearEntity = new Repository.Entities.Year()
        {
            Id = QA_Year_PATCH_PA_2020
        };

        GeneratedIdHelper.GenerateIdKeys(yearEntity);

        var loketContext = GetLoketContext();
        var administration = await loketContext.Set<Administration>()
            .FirstOrDefaultAsync(x => x.InheritanceLevelId == yearEntity.InheritanceLevelId);

        if (administration != null)
        {
            // Use SQL to bypass EF Core OUTPUT clause
            await loketContext.DbContext.Database.ExecuteSqlRawAsync(
                "UPDATE [Ulsa].[Werkgever] SET LoonheffingenNummer = {0} WHERE WerkgeverId = {1}",
                "123456782L35", yearEntity.InheritanceLevelId);
        }

        var payload = new YearPaPatchModel()
        {
            TestYear = true,
            SendEssMail = false,
            DateAvailableEss = new DateOnly(2025, 4, 30),
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2020.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_ZwSelfInsurerStartPayrollPeriod_Invalid"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_ZwSelfInsurerStartPayrollPeriod_Invalid()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = true,
            ZwSelfInsurerStartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 13 }, // Non-existent period
            SendEssMail = false,
            DateAvailableEss = new DateOnly(2025, 4, 30),
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2020.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_Aof_Invalid"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Aof_Invalid()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 3 }, // Non-existent AOF
            SendEssMail = false,
            DateAvailableEss = new DateOnly(2025, 4, 30),
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2022.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_Aof_MustNotHaveValueBefore2022"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Aof_MustNotHaveValueBefore2022()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = true,
            Aof = new KeyModel { Key = 1 },
            SendEssMail = false,
            DateAvailableEss = new DateOnly(2025, 4, 30),
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2020.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_Aof_MustHaveValueFrom2022"/>
    /// </summary>
    [Theory]
    [InlineData(null)]
    [InlineData(0)]
    public async Task BadRequest_MessageCode_Aof_MustHaveValueFrom2022(int? aof)
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = true,
            Aof = aof.HasValue ? new KeyModel { Key = aof.Value } : null,
            SendEssMail = false,
            DateAvailableEss = new DateOnly(2025, 4, 30),
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2022.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_Aof_Invalid"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_SendEssMail_Change_For_NonClosureRequestedOrClosedYear()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            SendEssMail = true,
            DateAvailableEss = null,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2025.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_MessageCode_SendEssMail_Change_For_YearWithDateAvailableEssInThePast()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            SendEssMail = true,
            DateAvailableEss = new DateOnly(2025, 4, 30),
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2022.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_MessageCode_DateAvailableEss_Change_For_NonClosureRequestedOrClosedYear()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            DateAvailableEss = new DateOnly(2099, 4, 30),
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2025.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_MessageCode_DateAvailableEss_Change_For_YearWithDateAvailableEssInThePast()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            DateAvailableEss = new DateOnly(2099, 4, 30),
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2022.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_MessageCode_DateAvailableEss_NotInTheFuture()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            DateAvailableEss = new DateOnly(2024, 4, 30),
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2024.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Fact]
    public async Task BadRequest_MessageCode_DateAvailableEss_NullForClosedYear()
    {
        // Arrange
        var payload = new YearPaPatchModel
        {
            TestYear = false,
            Aof = new KeyModel { Key = 1 },
            DateAvailableEss = null,
            SendEssMail = false,
        };

        // Act
        var patchUri = $"{YearRoutes.PatchYearByPayrollAdministrationYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_PA_2024.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }
}