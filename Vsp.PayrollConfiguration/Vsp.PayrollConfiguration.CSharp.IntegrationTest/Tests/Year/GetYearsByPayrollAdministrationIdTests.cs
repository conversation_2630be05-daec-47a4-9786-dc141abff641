using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Year.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Year;

[Collection(EntityNames.Year)]
public class GetYearsByPayrollAdministrationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Year";
    protected override bool UseTransaction => false;

    public static readonly Guid QA_Year_GET_PA = Guid.Parse("d8706816-6153-455a-934d-4e939a2af6c3");

    private const string OrderBy = "orderBy=year";
    private const string OrderByDesc = "orderBy=-year";

    private const string FilterYearId = "0000093d-07e8-0000-0000-000000000000"; // 2024
    private const string FilterUnexistentYearId = "0000093d-07e3-0000-0000-000000000000"; // 2019


    [Fact]
    public async Task Ok_OrderBy_Year()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByPayrollAdministrationIdAsync}?{OrderBy}".Replace("{payrollAdministrationId:guid}", QA_Year_GET_PA.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_OrderBy_Year_Desc()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByPayrollAdministrationIdAsync}?{OrderByDesc}".Replace("{payrollAdministrationId:guid}", QA_Year_GET_PA.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_FilterBy_Id()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByPayrollAdministrationIdAsync}?{OrderBy}&filter=id eq '{FilterYearId}'".Replace("{payrollAdministrationId:guid}", QA_Year_GET_PA.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_Empty()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByPayrollAdministrationIdAsync}?{OrderBy}&filter=id eq '{FilterUnexistentYearId}'".Replace("{payrollAdministrationId:guid}", QA_Year_GET_PA.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_Filter_Aof_Key()
    {
        // Act
        var getUri =
            $"{YearRoutes.GetYearsByPayrollAdministrationIdAsync}"
                .Replace("{payrollAdministrationId:guid}", QA_Year_GET_PA.ToString()) +
            $"?{OrderBy}" +
            $"&filter=aof.key eq 2"; // nullable code table
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}