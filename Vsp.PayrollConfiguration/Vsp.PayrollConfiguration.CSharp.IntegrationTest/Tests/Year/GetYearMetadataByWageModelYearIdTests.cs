using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Year.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Year;

[Collection(EntityNames.Year)]
public class GetYearMetadataByWageModelYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Year";
    protected override bool UseTransaction => false;

    [Theory]
    [InlineData("0000093c-07e4-0000-0000-000000000000")] // Year 2020 at WM level
    [InlineData("0000093c-07e5-0000-0000-000000000000")] // Year 2021 at WM level
    [InlineData("0000093c-07e6-0000-0000-000000000000")] // Year 2022 at WM level
    [InlineData("0000093c-07e7-0000-0000-000000000000")] // Year 2023 at WM level
    [InlineData("0000093c-07e8-0000-0000-000000000000")] // Year 2024 at WM level
    [InlineData("0000093c-07e9-0000-0000-000000000000")] // Year 2025 at WM level
    public async Task Ok(string yearId)
    {
        // Act
        var getUri = $"{YearRoutes.GetYearMetadataByWageModelYearIdAsync}".Replace("{yearId:guid}", yearId);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}
