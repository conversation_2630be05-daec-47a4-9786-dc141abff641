using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.UnitPercentage.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.UnitPercentage;

[Collection(EntityNames.UnitPercentage)]
public class PostUnitPercentageByInheritanceLevelIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "UnitPercentage";
    protected override bool UseTransaction => true;

    // Only year 2025
    private static readonly Guid QA_UnitPercentage_POST_CLA_ForLevel_CLA = Guid.Parse("53843707-7a6c-4f82-b445-f36636fb581a");
    private static readonly Guid QA_UnitPercentage_POST_WM_ForLevel_WM = Guid.Parse("973a7a96-0449-4bf3-95f4-3ee05bb8cc29");
    private static readonly Guid QA_UnitPercentage_POST_WM_ForLevel_PA = Guid.Parse("cda77a36-8ef6-4211-99dc-3ab360376146");
    private static readonly Guid QA_UnitPercentage_POST_PA_ForLevel_PA = Guid.Parse("d7764980-7211-4e63-b871-a516913ba872");

    // Years 2023 to 2025
    public static readonly Guid QA_UnitPercentage_POST_CLA_2023 = Guid.Parse("416c4f2f-fd06-49b3-8a15-dc3a44610ba6");
    public static readonly Guid QA_UnitPercentage_POST_WM_2023 = Guid.Parse("8e866a9e-e68a-46f9-9b44-c95921b12da3");
    public static readonly Guid QA_UnitPercentage_POST_PA_2023 = Guid.Parse("ef0f8893-c980-409b-897d-8f0c89cdb9c4");

    [Fact]
    public async Task Ok_QA_UnitPercentage_POST_CLA_ForLevel_CLA()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?collectiveLaborAgreementId={QA_UnitPercentage_POST_CLA_ForLevel_CLA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 1.234m,
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_POST_WM_ForLevel_WM()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?wageModelId={QA_UnitPercentage_POST_WM_ForLevel_WM}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 43 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 5.67m,
            CalculateOver = new() { Key = (int)CalculateOver.PeriodeUurloon },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_POST_PA_ForLevel_PA()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 44 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 8.9m,
            CalculateOver = new() { Key = (int)CalculateOver.GemiddeldUurloon },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    #region AddFutureYears

    [Fact]
    public async Task Ok_AddFutureYears()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_2023}";

        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 44 },
            Year = 2023,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 8.9m,
            CalculateOver = new() { Key = (int)CalculateOver.GemiddeldUurloon },
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_BadRequestInFutureYear()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_2023}";

        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 45 },
            Year = 2023,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 8.9m,
            CalculateOver = new() { Key = (int)CalculateOver.GemiddeldUurloon },
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);

        var loketContext = GetLoketContext();

        // New entity passes validation and therefore is added - and future year logic triggers
        var unitPercentage2023 = new Repository.Entities.UnitPercentage { InheritanceLevelId = 2445, YearId = 2023, ComponentId = 45, PayrollPeriodId = 1 };
        unitPercentage2023 = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(unitPercentage2023.Id))
            .SingleOrDefaultAsync();
        unitPercentage2023.Should().NotBeNull();

        // Next year skipped because of validation error (missing component 45 in year 2024)
        var unitPercentage2024 = new Repository.Entities.UnitPercentage { InheritanceLevelId = 2445, YearId = 2024, ComponentId = 45, PayrollPeriodId = 1 };
        unitPercentage2024 = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(unitPercentage2024.Id))
            .SingleOrDefaultAsync();
        unitPercentage2024.Should().BeNull();

        // But year after that is valid again and therefore added
        var unitPercentage2025 = new Repository.Entities.UnitPercentage { InheritanceLevelId = 2445, YearId = 2025, ComponentId = 45, PayrollPeriodId = 1 };
        unitPercentage2025 = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(unitPercentage2025.Id))
            .SingleOrDefaultAsync();
        unitPercentage2025.Should().NotBeNull();
    }

    [Fact]
    public async Task Ok_AddFutureYears_BadRequestsInFutureYears()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_2023}";

        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 46 },
            Year = 2023,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 8.9m,
            CalculateOver = new() { Key = (int)CalculateOver.GemiddeldUurloon },
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);

        var loketContext = GetLoketContext();

        // New entity passes validation and therefore is added - and future year logic triggers
        var unitPercentage2023 = new Repository.Entities.UnitPercentage { InheritanceLevelId = 2445, YearId = 2023, ComponentId = 46, PayrollPeriodId = 1 };
        unitPercentage2023 = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(unitPercentage2023.Id))
            .SingleOrDefaultAsync();
        unitPercentage2023.Should().NotBeNull();

        // Next year skipped because of validation error (missing component 46 in year 2024)
        var unitPercentage2024 = new Repository.Entities.UnitPercentage { InheritanceLevelId = 2445, YearId = 2024, ComponentId = 46, PayrollPeriodId = 1 };
        unitPercentage2024 = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(unitPercentage2024.Id))
            .SingleOrDefaultAsync();
        unitPercentage2024.Should().BeNull();

        // And year after that is also skipped because of validation error (missing component 46 in year 2025)
        var unitPercentage2025 = new Repository.Entities.UnitPercentage { InheritanceLevelId = 2445, YearId = 2025, ComponentId = 46, PayrollPeriodId = 1 };
        unitPercentage2025 = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(unitPercentage2025.Id))
            .SingleOrDefaultAsync();
        unitPercentage2025.Should().BeNull();
    }

    [Fact]
    public async Task BadRequest_AddFutureYears_NoFutureYearsAdded()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_2023}";

        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 45 }, // Component 45 is missing in year 2024
            Year = 2024,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 8.9m,
            CalculateOver = new() { Key = (int)CalculateOver.GemiddeldUurloon },
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);

        var loketContext = GetLoketContext();

        // New entity fails validation and is therefore NOT added
        var unitPercentage2024 = new Repository.Entities.UnitPercentage { InheritanceLevelId = 2445, YearId = 2024, ComponentId = 45, PayrollPeriodId = 1 };
        unitPercentage2024 = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(unitPercentage2024.Id))
            .SingleOrDefaultAsync();
        unitPercentage2024.Should().BeNull();

        // Next year entity would pass validation - but MAIN entity failed validation so next year logic is never triggered
        var unitPercentage2025 = new Repository.Entities.UnitPercentage { InheritanceLevelId = 2445, YearId = 2025, ComponentId = 45, PayrollPeriodId = 1 };
        unitPercentage2025 = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(unitPercentage2025.Id))
            .SingleOrDefaultAsync();
        unitPercentage2025.Should().BeNull();
    }

    #endregion

    #region ModelValidation

    [Fact]
    public async Task BadRequest_ModelValidation_Null()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Properties_Null()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel();
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_SubProperties_Null()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel { PayrollComponent = new(), StartPayrollPeriod = new(), Percentage = 1, CalculateOver = new() };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Percentage_TooSmall()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 0,
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Percentage_TooBig()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Percentage = 1000,
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Percentage_TooManyDecimals()
    {
        // Act
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postJson =
            """
            {
                "PayrollComponent": { "Key": 42 },
                "Year": 2025,
                "StartPayrollPeriod": { "PeriodNumber": 1 },
                "Percentage": 0.1234,
                "CalculateOver": { "Key": 0 }
            }
            """;
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postJson, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    #endregion

    #region MessageCode

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_UnitPercentage_PayrollComponent_Invalid"/>
    /// </summary>
    [Theory]
    [InlineData(15)] // Not a unit component
    [InlineData(41)] // Non-existing component
    public async Task BadRequest_MessageCode_PayrollComponent_Invalid(int component)
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = component },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 14.123m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    #endregion

    #region PATCH validator

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_UnitPercentage_CalculateOver_Invalid"/>
    /// </summary>
    [Theory]
    [InlineData(-1)]
    [InlineData(3)]
    public async Task BadRequest_MessageCode_CalculateOver_Invalid(int calculateOver)
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            CalculateOver = new() { Key = calculateOver },
            Percentage = 14.123m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    #endregion

    #region Inheritance Entity POST validator

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Year_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Year_DoesNotExist()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2024,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 14.123m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_DoesNotExist()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 13 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 14.123m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodDoesNotExist()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 2 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 14.123m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Entity_AlreadyExists_CurrentInheritanceLevel"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Entity_AlreadyExists_CurrentInheritanceLevel()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 14.123m,
        };
        await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Entity_AlreadyExists_ParentInheritanceLevel"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Entity_AlreadyExists_ParentInheritanceLevel()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?wageModelId={QA_UnitPercentage_POST_WM_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 14.123m,
        };
        await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}" +
            $"?payrollAdministrationId={QA_UnitPercentage_POST_PA_ForLevel_PA}";

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    #endregion
}