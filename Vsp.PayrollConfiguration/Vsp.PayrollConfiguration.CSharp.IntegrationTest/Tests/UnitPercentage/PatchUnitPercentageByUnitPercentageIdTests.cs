using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.UnitPercentage.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.UnitPercentage;

[Collection(EntityNames.UnitPercentage)]
public class PatchUnitPercentageByUnitPercentageIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "UnitPercentage";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_UnitPercentage_PATCH_CLA_ForLevel_CLA = Guid.Parse("d9e9b2ab-4ff6-4193-b3c0-a41f5815971c");
    private static readonly Guid QA_UnitPercentage_PATCH_WM_ForLevel_WM = Guid.Parse("8aa875b4-e58c-4837-83a2-55ef01a384f2");
    private static readonly Guid QA_UnitPercentage_PATCH_PA_ForLevel_PA = Guid.Parse("d134e58d-0d60-4f6d-8762-01ba0a1f84bd");

    private static readonly Guid QA_UnitPercentage_PATCH_PA_UP1 = Guid.Parse("00000912-07e9-002a-0100-000000000000");

    [Fact]
    public async Task Ok_QA_UnitPercentage_PATCH_CLA_ForLevel_CLA()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_UnitPercentage_PATCH_CLA_ForLevel_CLA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            CalculateOver = new() { Key = (int)CalculateOver.GemiddeldUurloon },
            Percentage = 4.1m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);
        var postObject = JsonConvert.DeserializeObject<DetailResult<UnitPercentageModel>>(postResult!)!;
        var unitPercentageId = postObject.Content!.Id;

        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", unitPercentageId.ToString());
        var patchModel = new UnitPercentagePatchModel
        {
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 3.3m,
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_PATCH_WM_ForLevel_WM()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}?wageModelId={QA_UnitPercentage_PATCH_WM_ForLevel_WM}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 5.12m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);
        var postObject = JsonConvert.DeserializeObject<DetailResult<UnitPercentageModel>>(postResult!)!;
        var unitPercentageId = postObject.Content!.Id;

        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", unitPercentageId.ToString());
        var patchModel = new UnitPercentagePatchModel
        {
            CalculateOver = new() { Key = (int)CalculateOver.PeriodeUurloon },
            Percentage = 6.32m,
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_PATCH_PA_ForLevel_PA()
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}?payrollAdministrationId={QA_UnitPercentage_PATCH_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 4 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 14.123m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);
        var postObject = JsonConvert.DeserializeObject<DetailResult<UnitPercentageModel>>(postResult!)!;
        var unitPercentageId = postObject.Content!.Id;

        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", unitPercentageId.ToString());
        var patchModel = new UnitPercentagePatchModel
        {
            CalculateOver = new() { Key = (int)CalculateOver.GemiddeldUurloon },
            Percentage = 11.321m,
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Null()
    {
        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", Guid.Empty.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Percentage_CalculateOver_Null()
    {
        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", Guid.Empty.ToString());
        var patchModel = new UnitPercentagePatchModel();
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_CalculateOver_Key_Null()
    {
        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", Guid.Empty.ToString());
        var patchModel = new UnitPercentagePatchModel { Percentage = 1, CalculateOver = new() };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Percentage_TooSmall()
    {
        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", Guid.Empty.ToString());
        var patchModel = new UnitPercentagePatchModel { Percentage = 0, CalculateOver = new() { Key = 0 } };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Percentage_TooBig()
    {
        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", Guid.Empty.ToString());
        var patchModel = new UnitPercentagePatchModel { Percentage = 1000, CalculateOver = new() { Key = 0 } };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Percentage_TooManyDecimals()
    {
        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", Guid.Empty.ToString());
        var patchJson =
            """
            {
                "Percentage": 0.1234,
                "CalculateOver": { "Key": 0 }
            }
            """;
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchJson, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_UnitPercentage_CalculateOver_Invalid"/>
    /// </summary>
    [Theory]
    [InlineData(-1)]
    [InlineData(3)]
    public async Task BadRequest_MessageCode_CalculateOver_Invalid(int calculateOver)
    {
        // Arrange
        var postUri = $"{UnitPercentageRoutes.PostUnitPercentageByInheritanceLevelIdAsync}?payrollAdministrationId={QA_UnitPercentage_PATCH_PA_ForLevel_PA}";
        var postModel = new UnitPercentagePostModel
        {
            PayrollComponent = new() { Key = 42 },
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 4 },
            CalculateOver = new() { Key = (int)CalculateOver.BedragPerEenheid },
            Percentage = 14.123m,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);
        var postObject = JsonConvert.DeserializeObject<DetailResult<UnitPercentageModel>>(postResult!)!;
        var unitPercentageId = postObject.Content!.Id;

        // Act
        var patchUri = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", unitPercentageId.ToString());
        var patchModel = new UnitPercentagePatchModel { Percentage = 1, CalculateOver = new() { Key = calculateOver } };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_CalculateOver_Invalid_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{UnitPercentageRoutes.PatchUnitPercentageByUnitPercentageIdAsync}".Replace("{unitPercentageId:guid}", QA_UnitPercentage_PATCH_PA_UP1.ToString()),
                Method = HttpMethod.Patch,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = new UnitPercentagePatchModel { Percentage = 1, CalculateOver = new() { Key = -1 } }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure unit percentage was not updated
        using var loketContext = GetLoketContext();
        var unitPercentage = await loketContext.Set<Repository.Entities.UnitPercentage>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.UnitPercentage>(QA_UnitPercentage_PATCH_PA_UP1))
            .SingleOrDefaultAsync();
        unitPercentage.Should().NotBeNull();
        unitPercentage.CalculateOver.Should().NotBe(-1);
    }
}