using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.UnitPercentage.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.UnitPercentage;

[Collection(EntityNames.UnitPercentage)]
public class DeleteUnitPercentageByUnitPercentageIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "UnitPercentage";
    protected override bool UseTransaction => true;

    private static class Ids
    {
        // In QA_UnitPercentage_DELETE_CLA_ForLevel_CLA
        public static readonly Guid UnitPercentage_CLA = Guid.Parse("00000901-07e9-0032-0100-000000000000");
        public static readonly Guid UnitPercentage_CLA_2025 = Guid.Parse("00000901-07e9-0000-0000-000000000000");

        // In QA_UnitPercentage_DELETE_WM_ForLevel_WM
        public static readonly Guid UnitPercentage_WM = Guid.Parse("00000903-07e9-0032-0100-000000000000");
        public static readonly Guid UnitPercentage_WM_2025 = Guid.Parse("00000903-07e9-0000-0000-000000000000");

        // In QA_UnitPercentage_DELETE_WM_ForLevel_PA
        public static readonly Guid UnitPercentage_WM_ForLevel_PA = Guid.Parse("00000905-07e9-0032-0100-000000000000");

        // In QA_UnitPercentage_DELETE_PA_ForLevel_PA
        public static readonly Guid UnitPercentage_PA_ForLevel_PA_2025 = Guid.Parse("00000906-07e9-0000-0000-000000000000");
        public static readonly Guid UnitPercentage_PA_Period1 = Guid.Parse("00000906-07e9-002a-0100-000000000000");
        public static readonly Guid UnitPercentage_PA_Period2 = Guid.Parse("00000906-07e9-002a-0200-000000000000");
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_DELETE_CLA_ForLevel_CLA()
    {
        // Act
        var deleteUri = $"{UnitPercentageRoutes.DeleteUnitPercentageByUnitPercentageIdAsync}"
            .Replace("{unitPercentageId:guid}", Ids.UnitPercentage_CLA.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(deleteResult);

        var getUri =
            $"{UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync}".Replace("{yearId:guid}", Ids.UnitPercentage_CLA_2025.ToString()) +
            $"?filter=id eq '{Ids.UnitPercentage_CLA}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<UnitPercentageModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_DELETE_WM_ForLevel_WM()
    {
        // Act
        var deleteUri = $"{UnitPercentageRoutes.DeleteUnitPercentageByUnitPercentageIdAsync}"
            .Replace("{unitPercentageId:guid}", Ids.UnitPercentage_WM.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(deleteResult);

        var getUri =
            $"{UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync}".Replace("{yearId:guid}", Ids.UnitPercentage_WM_2025.ToString()) +
            $"?filter=id eq '{Ids.UnitPercentage_WM}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<UnitPercentageModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_DELETE_PA_ForLevel_PA()
    {
        // Act
        var deleteUri = $"{UnitPercentageRoutes.DeleteUnitPercentageByUnitPercentageIdAsync}"
            .Replace("{unitPercentageId:guid}", Ids.UnitPercentage_PA_Period2.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(deleteResult);

        var getUri =
            $"{UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync}".Replace("{yearId:guid}", Ids.UnitPercentage_PA_ForLevel_PA_2025.ToString()) +
            $"?filter=id eq '{Ids.UnitPercentage_PA_Period2}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<UnitPercentageModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    #region Inheritance Entity DELETE validator

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Delete_EntityHasChildren"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_EntityHasChildren()
    {
        // Act
        var deleteUri = $"{UnitPercentageRoutes.DeleteUnitPercentageByUnitPercentageIdAsync}"
            .Replace("{unitPercentageId:guid}", Ids.UnitPercentage_WM_ForLevel_PA.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodCannotBeDeleted()
    {
        // Act
        var deleteUri = $"{UnitPercentageRoutes.DeleteUnitPercentageByUnitPercentageIdAsync}"
            .Replace("{unitPercentageId:guid}", Ids.UnitPercentage_PA_Period1.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    #endregion
}