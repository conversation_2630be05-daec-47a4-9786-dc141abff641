using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.PayrollComponentExtra.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollComponentExtra;

[Collection(EntityNames.PayrollComponentExtra)]
public class GetPayrollComponentExtraByPayrollComponentIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollComponentExtra";
    protected override bool UseTransaction => false;

    [Theory]
    [InlineData("00000978-07e8-0032-0000-000000000000")] // QA_PayrollComponentExtra_GET_CLA - Year 2024 - Component 50
    [InlineData("00000978-07e9-0032-0000-000000000000")] // QA_PayrollComponentExtra_GET_CLA - Year 2025 - Component 50
    public async Task Ok_QA_PayrollComponentExtra_GET_CLA(string payrollComponentId)
    {
        // Arrange
        var getUri = $"{PayrollComponentExtraRoutes.GetPayrollComponentExtraByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", payrollComponentId);

        // Act
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Theory]
    [InlineData("00000979-07e8-0032-0000-000000000000")] // QA_PayrollComponentExtra_GET_WM - Year 2024 - Component 50
    [InlineData("00000979-07e9-0032-0000-000000000000")] // QA_PayrollComponentExtra_GET_WM - Year 2025 - Component 50
    public async Task Ok_QA_PayrollComponentExtra_GET_WM(string payrollComponentId)
    {
        // Arrange
        var getUri = $"{PayrollComponentExtraRoutes.GetPayrollComponentExtraByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", payrollComponentId);

        // Act
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Theory]
    [InlineData("0000097a-07e8-0032-0000-000000000000")] // QA_PayrollComponentExtra_GET_PA - Year 2024 - Component 50
    [InlineData("0000097a-07e9-0032-0000-000000000000")] // QA_PayrollComponentExtra_GET_PA - Year 2025 - Component 50
    public async Task Ok_QA_PayrollComponentExtra_GET_PA(string payrollComponentId)
    {
        // Arrange
        var getUri = $"{PayrollComponentExtraRoutes.GetPayrollComponentExtraByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", payrollComponentId);

        // Act
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Theory]
    [InlineData("00000978-07e8-0033-0000-000000000000")] // QA_PayrollComponentExtra_GET_ONLY_Deviant_For_PA - CLA - Year 2024 - Component 51
    [InlineData("00000978-07e9-0033-0000-000000000000")] // QA_PayrollComponentExtra_GET_ONLY_Deviant_For_PA - CLA - Year 2025 - Component 51
    [InlineData("00000979-07e8-0033-0000-000000000000")] // QA_PayrollComponentExtra_GET_ONLY_Deviant_For_PA - WM - Year 2024 - Component 51
    [InlineData("00000979-07e9-0033-0000-000000000000")] // QA_PayrollComponentExtra_GET_ONLY_Deviant_For_PA - WM - Year 2025 - Component 51
    [InlineData("0000097a-07e8-0033-0000-000000000000")] // QA_PayrollComponentExtra_GET_ONLY_Deviant_For_PA - PA - Year 2024 - Component 51
    [InlineData("0000097a-07e9-0033-0000-000000000000")] // QA_PayrollComponentExtra_GET_ONLY_Deviant_For_PA - PA - Year 2025 - Component 51
    public async Task Ok_QA_PayrollComponentExtra_GET_ONLY_Deviant_For_PA_Level(string payrollComponentId)
    {
        // Arrange
        var getUri = $"{PayrollComponentExtraRoutes.GetPayrollComponentExtraByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", payrollComponentId);

        // Act
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}